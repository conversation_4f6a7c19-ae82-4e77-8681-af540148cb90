# 只对 本地-新闻 (id=212) 和 本地-微信朋友圈 (id=218) 做治理统计
from pyspark.sql import SparkSession
from datetime import datetime, timedelta

spark = SparkSession.builder \
    .appName("spark-app-process-count-local-only") \
    .getOrCreate()

gxkUrl = "*******************************************"
resultUrl = "************************************************"
gxkUsername = "admin"
gxkPassword = "trsadmin@1234"

data_process_record = spark.read \
    .format("jdbc") \
    .option("url", gxkUrl) \
    .option("dbtable", "data_process_record") \
    .option("user", gxkUsername) \
    .option("password", gxkPassword) \
    .option("driver", "com.clickhouse.jdbc.ClickHouseDriver") \
    .load()

data_process_count = spark.read \
    .format("jdbc") \
    .option("url", resultUrl) \
    .option("dbtable", "dws_display_data_process_count") \
    .option("user", gxkUsername) \
    .option("password", gxkPassword) \
    .option("driver", "com.clickhouse.jdbc.ClickHouseDriver") \
    .load()

data_process_record.createOrReplaceTempView("data_process_record")
data_process_count.createOrReplaceTempView("data_process_count")

def get_search_time():
    begin_time = datetime.now() - timedelta(days=3)
    search_begin_time = begin_time.strftime("%Y-%m-%d %H:00:00")
    end_time = datetime.now() - timedelta(hours=1)
    search_end_time = end_time.strftime("%Y-%m-%d %H:59:59")
    return (search_begin_time, search_end_time)

def split_time_by_hour(begin_time: datetime, end_time: datetime):
    time_range = []
    current_time = begin_time
    while current_time <= end_time:
        time_range.append(current_time.strftime("%Y-%m-%d %H:00:00"))
        current_time += timedelta(hours=1)
    return time_range

def create_default_time_table():
    search_begin_time, search_end_time = get_search_time()
    begin_time = datetime.strptime(search_begin_time, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(search_end_time, "%Y-%m-%d %H:%M:%S")
    time_range = split_time_by_hour(begin_time, end_time)
    default_time_table = []
    for time_point in time_range:
        row = {"key_as_string": time_point}
        default_time_table.append(row)
    default_time_df = spark.read.json(spark.sparkContext.parallelize([default_time_table]))
    default_time_df.createOrReplaceTempView("default_time_df")
    default_time_df.show()

create_default_time_table()


begin_time, end_time = get_search_time()

# 只统计 data_model_id = 212 (本地-新闻) -> news_count
#            data_model_id = 218 (本地-微信朋友圈) -> wxpyq_count
sql = f"""
SELECT
    SUM(CASE WHEN data_model_id = 212 THEN 1 ELSE 0 END) AS news_count,
    SUM(CASE WHEN data_model_id = 218 THEN 1 ELSE 0 END) AS wxpyq_count,
    SUM(CASE WHEN data_model_id IN (212, 218) THEN 1 ELSE 0 END) AS total,
    DATE_FORMAT(storage_time, 'yyyy-MM-dd HH:00:00') AS hour
FROM data_process_record
WHERE DATE_FORMAT(storage_time, 'yyyy-MM-dd HH:mm:ss') > '{begin_time}'
    AND DATE_FORMAT(storage_time, 'yyyy-MM-dd HH:mm:ss') < '{end_time}'
GROUP BY DATE_FORMAT(storage_time, 'yyyy-MM-dd HH:00:00')
"""

count_df = spark.sql(sql)
count_df.show(10)
count_df.createOrReplaceTempView("count_df")


result = spark.sql("""
    SELECT
       news_count,
       wxpyq_count,
       total,
       DATE_FORMAT(to_timestamp(default_time_df.key_as_string), 'yyyy年MM月dd日HH时') AS hour,
       DATE_FORMAT(to_timestamp(default_time_df.key_as_string), 'yyyy年MM月dd日')     AS day,
       DATE_FORMAT(to_timestamp(default_time_df.key_as_string), 'yyyy年MM月')         AS month,
       YEAR(to_timestamp(default_time_df.key_as_string))                              AS year,
       now()                                                                          AS insert_time,
       'trslc'                                                                        AS source
    FROM default_time_df
    LEFT JOIN count_df ON default_time_df.key_as_string = count_df.hour
    WHERE default_time_df.key_as_string > (
         SELECT DATE_FORMAT(to_timestamp(hour, 'yyyy年MM月dd日HH时'), 'yyyy-MM-dd HH:mm:ss')
         FROM data_process_count
         ORDER BY hour DESC
         LIMIT 1
    )
    ORDER BY default_time_df.key_as_string
""")


result.show(3)

resultUsername = "admin"
resultPassword = "trsadmin@1234"
result.write.format('jdbc') \
    .option("url", resultUrl) \
    .option("dbtable", "dws_display_data_process_count") \
    .option("user", resultUsername) \
    .option("password", resultPassword) \
    .option("driver", "com.clickhouse.jdbc.ClickHouseDriver") \
    .option("createTableOptions", "ENGINE = MergeTree() ORDER BY hour") \
    .mode("append") \
    .save()

spark.stop()
