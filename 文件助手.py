# -*- coding: utf-8 -*-
"""
SFTP 文件下载助手

功能:
- 连接指定 SFTP 服务器与文件夹
- 按“模糊”规则筛选文件 (子串/通配/glob/正则)
- 并发下载至本地指定目录，支持断点跳过(按大小判断)
- 可递归遍历子目录

依赖: pip install paramiko

用法示例:
python 文件助手.py \
  --host ******* --port 22 --user yourname --password yourpass \
  --remote-dir /data/export \
  --local-dir ./downloads \
  --pattern "*.csv" --pattern-type glob \
  --concurrency 8 --recursive

或使用密钥:
python 文件助手.py \
  --host ******* --user yourname --pkey ~/.ssh/id_rsa --pkey-passphrase "xxxx" \
  --remote-dir /data/export --local-dir ./downloads -p report -t substr -c 6
"""

from __future__ import annotations

import argparse
import fnmatch
import logging
import os
import posixpath
import queue
import re
import stat as pystat
import threading
import time
from dataclasses import dataclass
from typing import Iterable, List, Optional, Tuple

import paramiko


@dataclass
class SFTPConfig:
    host: str
    port: int = 22
    username: str = ""
    password: Optional[str] = None
    key_filename: Optional[str] = None
    passphrase: Optional[str] = None
    timeout: int = 15
    banner_timeout: int = 15
    auth_timeout: int = 15
    compress: bool = True


def create_sftp_client(cfg: SFTPConfig) -> Tuple[paramiko.SSHClient, paramiko.SFTPClient]:
    client = paramiko.SSHClient()
    # 自动接受未知主机指纹。生产环境建议改为加载 known_hosts 并严格校验。
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    client.connect(
        hostname=cfg.host,
        port=cfg.port,
        username=cfg.username,
        password=cfg.password,
        key_filename=cfg.key_filename,
        passphrase=cfg.passphrase,
        timeout=cfg.timeout,
        banner_timeout=cfg.banner_timeout,
        auth_timeout=cfg.auth_timeout,
        compress=cfg.compress,
        look_for_keys=False if cfg.key_filename else True,
    )
    transport = client.get_transport()
    if transport is not None:
        transport.set_keepalive(10)
    sftp = client.open_sftp()
    return client, sftp


def matches(name: str, patterns: Iterable[str], mode: str = "glob", ignore_case: bool = False) -> bool:
    if not patterns:
        return True
    if ignore_case:
        name_cmp = name.lower()
        pats = [p.lower() for p in patterns]
    else:
        name_cmp = name
        pats = list(patterns)

    if mode == "substr":
        return any(p in name_cmp for p in pats)
    elif mode == "regex":
        flags = re.IGNORECASE if ignore_case else 0
        return any(re.search(p, name, flags=flags) is not None for p in patterns)
    else:  # glob
        if ignore_case:
            return any(fnmatch.fnmatch(name_cmp, p) for p in pats)
        else:
            # fnmatch.fnmatch 已经是大小写无关/有关的混合实现，不同平台行为不同；用 fnmatchcase 保持显式。
            return any(fnmatch.fnmatchcase(name, p) for p in patterns)


def ensure_dir(path: str) -> None:
    if path and not os.path.exists(path):
        os.makedirs(path, exist_ok=True)


def list_remote_files(
    sftp: paramiko.SFTPClient, remote_dir: str, recursive: bool, logger: logging.Logger
) -> List[Tuple[str, paramiko.SFTPAttributes]]:
    """
    返回: [(remote_rel_path, attrs), ...]
    remote_rel_path 以 remote_dir 为根的相对 POSIX 路径
    """
    results: List[Tuple[str, paramiko.SFTPAttributes]] = []

    def _walk(dir_path: str, rel_prefix: str = ""):
        try:
            entries = sftp.listdir_attr(dir_path)
        except FileNotFoundError:
            logger.error("远程目录不存在: %s", dir_path)
            return
        except Exception as e:
            logger.error("列目录失败 %s: %s", dir_path, e)
            return
        for attr in entries:
            name = attr.filename
            rel_path = name if not rel_prefix else f"{rel_prefix}/{name}"
            full_path = posixpath.join(dir_path, name)
            mode = attr.st_mode
            if pystat.S_ISDIR(mode):
                if recursive:
                    _walk(full_path, rel_path)
            else:
                results.append((rel_path, attr))

    _walk(remote_dir, "")
    return results


def download_worker(
    cfg: SFTPConfig,
    remote_dir: str,
    local_dir: str,
    skip_existing: bool,
    task_q: queue.Queue,
    result: dict,
    logger: logging.Logger,
):
    client = None
    sftp = None
    try:
        client, sftp = create_sftp_client(cfg)
        while True:
            item = task_q.get()
            if item is None:
                task_q.task_done()
                break
            rel_path, attr = item
            remote_path = posixpath.join(remote_dir, rel_path)
            local_path = os.path.join(local_dir, rel_path.replace("/", os.sep))
            ensure_dir(os.path.dirname(local_path))
            try:
                if skip_existing and os.path.exists(local_path):
                    if os.path.getsize(local_path) == getattr(attr, "st_size", None):
                        logger.debug("跳过(已存在且大小一致): %s", rel_path)
                        result["skipped"] += 1
                        continue
                sftp.get(remote_path, local_path)
                logger.info("下载完成: %s -> %s", remote_path, local_path)
                result["downloaded"] += 1
            except Exception as e:
                logger.error("下载失败 %s: %s", rel_path, e)
                result["failed"] += 1
            finally:
                task_q.task_done()
    finally:
        try:
            if sftp:
                sftp.close()
        finally:
            if client:
                client.close()


def run_download(
    cfg: SFTPConfig,
    remote_dir: str,
    local_dir: str,
    patterns: List[str],
    pattern_type: str,
    ignore_case: bool,
    concurrency: int,
    recursive: bool,
    skip_existing: bool,
    logger: logging.Logger,
) -> dict:
    start = time.time()
    # 先用一个连接完成清单获取，避免多次重复列目录
    client, sftp = create_sftp_client(cfg)
    try:
        all_files = list_remote_files(sftp, remote_dir, recursive, logger)
    finally:
        try:
            sftp.close()
        finally:
            client.close()

    filtered = [(p, a) for (p, a) in all_files if matches(p.split("/")[-1], patterns, pattern_type, ignore_case)]
    total = len(filtered)
    logger.info("待下载文件数: %d / 发现总数: %d", total, len(all_files))

    task_q: queue.Queue = queue.Queue(maxsize=concurrency * 2 or 4)
    result = {"downloaded": 0, "failed": 0, "skipped": 0}

    threads: List[threading.Thread] = []
    workers = max(1, concurrency)
    for i in range(workers):
        t = threading.Thread(
            target=download_worker,
            name=f"sftp-dl-{i}",
            args=(cfg, remote_dir, local_dir, skip_existing, task_q, result, logger),
            daemon=True,
        )
        t.start()
        threads.append(t)

    for item in filtered:
        task_q.put(item)

    # 结束信号
    for _ in range(workers):
        task_q.put(None)

    task_q.join()
    for t in threads:
        t.join()

    elapsed = time.time() - start
    logger.info(
        "完成: 下载=%d, 跳过=%d, 失败=%d, 用时=%.2fs",
        result["downloaded"],
        result["skipped"],
        result["failed"],
        elapsed,
    )
    result.update({"total": total, "elapsed": elapsed})
    return result


def build_logger(verbosity: int) -> logging.Logger:
    logger = logging.getLogger("sftp_downloader")
    logger.setLevel(logging.DEBUG)
    ch = logging.StreamHandler()
    level = logging.WARNING
    if verbosity == 1:
        level = logging.INFO
    elif verbosity >= 2:
        level = logging.DEBUG
    ch.setLevel(level)
    fmt = logging.Formatter("[%(asctime)s] %(levelname)s: %(message)s", datefmt="%H:%M:%S")
    ch.setFormatter(fmt)
    if logger.handlers:
        logger.handlers.clear()
    logger.addHandler(ch)
    return logger


def parse_args(argv: Optional[List[str]] = None) -> argparse.Namespace:
    p = argparse.ArgumentParser(description="SFTP 模糊筛选并发下载工具")
    p.add_argument("--host", required=True, help="SFTP 主机")
    p.add_argument("--port", type=int, default=22, help="端口，默认 22")
    p.add_argument("--user", required=True, help="用户名")
    auth = p.add_argument_group("认证")
    auth.add_argument("--password", help="密码")
    auth.add_argument("--pkey", dest="key_filename", help="私钥路径，如 ~/.ssh/id_rsa")
    auth.add_argument("--pkey-passphrase", dest="passphrase", help="私钥口令")

    p.add_argument("--remote-dir", required=True, help="远程目录(绝对路径)")
    p.add_argument("--local-dir", required=True, help="本地保存目录")

    p.add_argument("-p", "--pattern", action="append", default=[], help="匹配模式，可多次提供。默认匹配全部")
    p.add_argument(
        "-t",
        "--pattern-type",
        choices=["substr", "glob", "regex"],
        default="glob",
        help="匹配类型: substr|glob|regex，默认 glob",
    )
    p.add_argument("-i", "--ignore-case", action="store_true", help="匹配忽略大小写")

    p.add_argument("-c", "--concurrency", type=int, default=6, help="并发线程数，默认 6")
    p.add_argument("-r", "--recursive", action="store_true", help="递归子目录")

    sk = p.add_argument_group("覆盖/跳过")
    sk.add_argument("--force", action="store_true", help="强制覆盖已存在文件")

    p.add_argument("-v", "--verbose", action="count", default=1, help="日志详尽程度，可叠加")

    args = p.parse_args(argv)
    return args


def main(argv: Optional[List[str]] = None) -> int:
    args = parse_args(argv)
    logger = build_logger(args.verbose)

    cfg = SFTPConfig(
        host=args.host,
        port=args.port,
        username=args.user,
        password=args.password,
        key_filename=args.key_filename,
        passphrase=args.passphrase,
    )

    patterns = args.pattern or []
    if not patterns:
        # 无 pattern 则匹配全部
        patterns = ["*"] if args.pattern_type == "glob" else [""]

    skip_existing = not args.force

    ensure_dir(args.local_dir)

    result = run_download(
        cfg=cfg,
        remote_dir=args.remote_dir,
        local_dir=args.local_dir,
        patterns=patterns,
        pattern_type=args.pattern_type,
        ignore_case=args.ignore_case,
        concurrency=max(1, args.concurrency),
        recursive=args.recursive,
        skip_existing=skip_existing,
        logger=logger,
    )

    return 0 if result.get("failed", 0) == 0 else 2


if __name__ == "__main__":
    raise SystemExit(main())
