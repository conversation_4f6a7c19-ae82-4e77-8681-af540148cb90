11:01:05  开始执行任务：任务01
11:01:07  25/08/15 11:01:07 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
11:01:09  25/08/15 11:01:09 INFO SparkContext: Running Spark version 3.5.4
11:01:09  25/08/15 11:01:09 INFO SparkContext: OS info Linux, 4.18.0-193.28.1.el7.aarch64, aarch64
11:01:09  25/08/15 11:01:09 INFO SparkContext: Java version 17.0.14
11:01:09  25/08/15 11:01:09 INFO ResourceUtils: ==============================================================
11:01:09  25/08/15 11:01:09 INFO ResourceUtils: No custom resources configured for spark.driver.
11:01:09  25/08/15 11:01:09 INFO ResourceUtils: ==============================================================
11:01:09  25/08/15 11:01:09 INFO SparkContext: Submitted application: spark-app-process-count-local-only
11:01:09  25/08/15 11:01:09 INFO ResourceProfile: Default ResourceProfile created, executor resources: Map(cores -> name: cores, amount: 2, script: , vendor: , memory -> name: memory, amount: 1024, script: , vendor: , offHeap -> name: offHeap, amount: 0, script: , vendor: ), task resources: Map(cpus -> name: cpus, amount: 1.0)
11:01:09  25/08/15 11:01:09 INFO ResourceProfile: Limiting resource is cpus at 2 tasks per executor
11:01:09  25/08/15 11:01:09 INFO ResourceProfileManager: Added ResourceProfile id: 0
11:01:09  25/08/15 11:01:09 INFO SecurityManager: Changing view acls to: root
11:01:09  25/08/15 11:01:09 INFO SecurityManager: Changing modify acls to: root
11:01:09  25/08/15 11:01:09 INFO SecurityManager: Changing view acls groups to:
11:01:09  25/08/15 11:01:09 INFO SecurityManager: Changing modify acls groups to:
11:01:09  25/08/15 11:01:09 INFO SecurityManager: SecurityManager: authentication disabled; ui acls disabled; users with view permissions: root; groups with view permissions: EMPTY; users with modify permissions: root; groups with modify permissions: EMPTY
11:01:09  25/08/15 11:01:09 WARN Utils: Service 'sparkDriver' could not bind on port 40000. Attempting port 40001.
11:01:09  25/08/15 11:01:09 INFO Utils: Successfully started service 'sparkDriver' on port 40001.
11:01:09  25/08/15 11:01:09 INFO SparkEnv: Registering MapOutputTracker
11:01:09  25/08/15 11:01:09 INFO SparkEnv: Registering BlockManagerMaster
11:01:09  25/08/15 11:01:09 INFO BlockManagerMasterEndpoint: Using org.apache.spark.storage.DefaultTopologyMapper for getting topology information
11:01:09  25/08/15 11:01:09 INFO BlockManagerMasterEndpoint: BlockManagerMasterEndpoint up
11:01:09  25/08/15 11:01:09 INFO SparkEnv: Registering BlockManagerMasterHeartbeat
11:01:09  25/08/15 11:01:09 INFO DiskBlockManager: Created local directory at /tmp/blockmgr-95680fa2-e48e-40a8-ba03-ff330e98f113
11:01:09  25/08/15 11:01:09 INFO MemoryStore: MemoryStore started with capacity 434.4 MiB
11:01:09  25/08/15 11:01:09 INFO SparkEnv: Registering OutputCommitCoordinator
11:01:09  25/08/15 11:01:09 INFO JettyUtils: Start Jetty 0.0.0.0:4040 for SparkUI
11:01:09  25/08/15 11:01:09 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.
11:01:09  25/08/15 11:01:09 INFO Utils: Successfully started service 'SparkUI' on port 4041.
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/commons-pool2-2.11.1.jar at spark://************:40001/jars/commons-pool2-2.11.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/lang-mustache-client-7.12.1.jar at spark://************:40001/jars/lang-mustache-client-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/jedis-3.8.0.jar at spark://************:40001/jars/jedis-3.8.0.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/hppc-0.8.1.jar at spark://************:40001/jars/hppc-0.8.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/rank-eval-client-7.12.1.jar at spark://************:40001/jars/rank-eval-client-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpcore5-h2-5.2.4.jar at spark://************:40001/jars/httpcore5-h2-5.2.4.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/commons-httpclient-3.1.jar at spark://************:40001/jars/commons-httpclient-3.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/postgresql-42.7.4.jar at spark://************:40001/jars/postgresql-42.7.4.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/lucene-core-8.8.0.jar at spark://************:40001/jars/lucene-core-8.8.0.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/SparkOnEs-1.0.jar at spark://************:40001/jars/SparkOnEs-1.0.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/spark-redis_2.12-3.1.0.jar at spark://************:40001/jars/spark-redis_2.12-3.1.0.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/fastjson-1.2.83.jar at spark://************:40001/jars/fastjson-1.2.83.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-rest-high-level-client-7.12.1.jar at spark://************:40001/jars/elasticsearch-rest-high-level-client-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpcore-nio-4.4.16.jar at spark://************:40001/jars/httpcore-nio-4.4.16.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/clickhouse-jdbc-0.6.4.jar at spark://************:40001/jars/clickhouse-jdbc-0.6.4.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-x-content-7.12.1.jar at spark://************:40001/jars/elasticsearch-x-content-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-7.12.1.jar at spark://************:40001/jars/elasticsearch-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/hybase-v10.0.5.jar at spark://************:40001/jars/hybase-v10.0.5.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpclient5-5.3.1.jar at spark://************:40001/jars/httpclient5-5.3.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/protobuf-java-3.21.12.jar at spark://************:40001/jars/protobuf-java-3.21.12.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpasyncclient-4.1.5.jar at spark://************:40001/jars/httpasyncclient-4.1.5.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/hive-jdbc-3.1.0-h0.cbu.mrs.321.r13.jar at spark://************:40001/jars/hive-jdbc-3.1.0-h0.cbu.mrs.321.r13.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-core-7.12.1.jar at spark://************:40001/jars/elasticsearch-core-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpcore5-5.2.4.jar at spark://************:40001/jars/httpcore5-5.2.4.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-spark-30_2.12-7.12.0.jar at spark://************:40001/jars/elasticsearch-spark-30_2.12-7.12.0.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/mysql-connector-j-8.1.0.jar at spark://************:40001/jars/mysql-connector-j-8.1.0.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-rest-client-7.12.1.jar at spark://************:40001/jars/elasticsearch-rest-client-7.12.1.jar with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO SparkContext: Added file file:///home/<USER>/py-files/hybaseapi_.zip at spark://************:40001/files/hybaseapi_.zip with timestamp 1755226869057
11:01:09  25/08/15 11:01:09 INFO Utils: Copying /home/<USER>/py-files/hybaseapi_.zip to /tmp/spark-347ef113-1130-4d82-bf80-16cef44bae89/userFiles-89cbb703-cf4b-4585-bbd3-8ca7bebc0b8b/hybaseapi_.zip
11:01:10  25/08/15 11:01:10 INFO StandaloneAppClient$ClientEndpoint: Connecting to master spark://spark-manager:7077...
11:01:10  25/08/15 11:01:10 INFO TransportClientFactory: Successfully created connection to spark-manager/************:7077 after 29 ms (0 ms spent in bootstraps)
11:01:10  25/08/15 11:01:10 INFO StandaloneSchedulerBackend: Connected to Spark cluster with app ID app-20250815030110-1082
11:01:10  25/08/15 11:01:10 INFO StandaloneAppClient$ClientEndpoint: Executor added: app-20250815030110-1082/0 on worker-20250811151921-**********-39619 (**********:39619) with 2 core(s)
11:01:10  25/08/15 11:01:10 INFO StandaloneSchedulerBackend: Granted executor ID app-20250815030110-1082/0 on hostPort **********:39619 with 2 core(s), 1024.0 MiB RAM
11:01:10  25/08/15 11:01:10 INFO StandaloneAppClient$ClientEndpoint: Executor added: app-20250815030110-1082/1 on worker-20250811151924-**********-43349 (**********:43349) with 2 core(s)
11:01:10  25/08/15 11:01:10 INFO StandaloneSchedulerBackend: Granted executor ID app-20250815030110-1082/1 on hostPort **********:43349 with 2 core(s), 1024.0 MiB RAM
11:01:10  25/08/15 11:01:10 WARN Utils: Service 'org.apache.spark.network.netty.NettyBlockTransferService' could not bind on port 40200. Attempting port 40201.
11:01:10  25/08/15 11:01:10 INFO Utils: Successfully started service 'org.apache.spark.network.netty.NettyBlockTransferService' on port 40201.
11:01:10  25/08/15 11:01:10 INFO NettyBlockTransferService: Server created on ************ 0.0.0.0:40201
11:01:10  25/08/15 11:01:10 INFO BlockManager: Using org.apache.spark.storage.RandomBlockReplicationPolicy for block replication policy
11:01:10  25/08/15 11:01:10 INFO StandaloneAppClient$ClientEndpoint: Executor updated: app-20250815030110-1082/0 is now RUNNING
11:01:10  25/08/15 11:01:10 INFO StandaloneAppClient$ClientEndpoint: Executor updated: app-20250815030110-1082/1 is now RUNNING
11:01:10  25/08/15 11:01:10 INFO BlockManagerMaster: Registering BlockManager BlockManagerId(driver, ************, 40201, None)
11:01:10  25/08/15 11:01:10 INFO BlockManagerMasterEndpoint: Registering block manager ************:40201 with 434.4 MiB RAM, BlockManagerId(driver, ************, 40201, None)
11:01:10  25/08/15 11:01:10 INFO BlockManagerMaster: Registered BlockManager BlockManagerId(driver, ************, 40201, None)
11:01:10  25/08/15 11:01:10 INFO BlockManager: Initialized BlockManager: BlockManagerId(driver, ************, 40201, None)
11:01:10  25/08/15 11:01:10 INFO StandaloneSchedulerBackend: SchedulerBackend is ready for scheduling beginning after reached minRegisteredResourcesRatio: 0.0
11:01:10  25/08/15 11:01:10 INFO SharedState: Setting hive.metastore.warehouse.dir ('null') to the value of spark.sql.warehouse.dir.
11:01:10  25/08/15 11:01:10 INFO SharedState: Warehouse path is 'file:/opt/spark-warehouse'.
11:01:11  25/08/15 11:01:11 INFO Version: Elasticsearch Hadoop v7.12.0 [79dace1877]
11:01:11  25/08/15 11:01:11 INFO ApacheHttpConnectionImpl: Connection TTL: 0 ms
11:01:11  25/08/15 11:01:11 INFO ApacheHttpConnectionImpl: Connection reuse strategy: LIFO
11:01:12  25/08/15 11:01:12 INFO ApacheHttpConnectionImpl: Connection TTL: 0 ms
11:01:12  25/08/15 11:01:12 INFO ApacheHttpConnectionImpl: Connection reuse strategy: LIFO
11:01:13  25/08/15 11:01:13 INFO StandaloneSchedulerBackend$StandaloneDriverEndpoint: Registered executor NettyRpcEndpointRef(spark-client://Executor) (2.49.0.0:25063) with ID 1,  ResourceProfileId 0
11:01:13  25/08/15 11:01:13 INFO StandaloneSchedulerBackend$StandaloneDriverEndpoint: Registered executor NettyRpcEndpointRef(spark-client://Executor) (2.49.0.0:63504) with ID 0,  ResourceProfileId 0
11:01:13  25/08/15 11:01:13 INFO BlockManagerMasterEndpoint: Registering block manager **********:40201 with 434.4 MiB RAM, BlockManagerId(1, **********, 40201, None)
11:01:13  25/08/15 11:01:13 INFO BlockManagerMasterEndpoint: Registering block manager **********:40201 with 434.4 MiB RAM, BlockManagerId(0, **********, 40201, None)
11:01:14  25/08/15 11:01:14 INFO CodeGenerator: Code generated in 185.784343 ms
11:01:15  25/08/15 11:01:15 INFO SparkContext: Starting job: json at NativeMethodAccessorImpl.java:0
11:01:15  25/08/15 11:01:15 INFO DAGScheduler: Got job 0 (json at NativeMethodAccessorImpl.java:0) with 2 output partitions
11:01:15  25/08/15 11:01:15 INFO DAGScheduler: Final stage: ResultStage 0 (json at NativeMethodAccessorImpl.java:0)
11:01:15  25/08/15 11:01:15 INFO DAGScheduler: Parents of final stage: List()
11:01:15  25/08/15 11:01:15 INFO DAGScheduler: Missing parents: List()
11:01:15  25/08/15 11:01:15 INFO DAGScheduler: Submitting ResultStage 0 (MapPartitionsRDD[6] at json at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:15  25/08/15 11:01:15 INFO MemoryStore: Block broadcast_0 stored as values in memory (estimated size 27.0 KiB, free 434.4 MiB)
11:01:15  25/08/15 11:01:15 INFO MemoryStore: Block broadcast_0_piece0 stored as bytes in memory (estimated size 11.1 KiB, free 434.4 MiB)
11:01:15  25/08/15 11:01:15 INFO BlockManagerInfo: Added broadcast_0_piece0 in memory on ************:40201 (size: 11.1 KiB, free: 434.4 MiB)
11:01:15  25/08/15 11:01:15 INFO SparkContext: Created broadcast 0 from broadcast at DAGScheduler.scala:1585
11:01:15  25/08/15 11:01:15 INFO DAGScheduler: Submitting 2 missing tasks from ResultStage 0 (MapPartitionsRDD[6] at json at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0, 1))
11:01:15  25/08/15 11:01:15 INFO TaskSchedulerImpl: Adding task set 0.0 with 2 tasks resource profile 0
11:01:15  25/08/15 11:01:15 INFO TaskSetManager: Starting task 0.0 in stage 0.0 (TID 0) (**********, executor 0, partition 0, PROCESS_LOCAL, 11244 bytes)
11:01:15  25/08/15 11:01:15 INFO TaskSetManager: Starting task 1.0 in stage 0.0 (TID 1) (**********, executor 1, partition 1, PROCESS_LOCAL, 13231 bytes)
11:01:15  25/08/15 11:01:15 INFO BlockManagerInfo: Added broadcast_0_piece0 in memory on **********:40201 (size: 11.1 KiB, free: 434.4 MiB)
11:01:15  25/08/15 11:01:15 INFO BlockManagerInfo: Added broadcast_0_piece0 in memory on **********:40201 (size: 11.1 KiB, free: 434.4 MiB)
11:01:16  25/08/15 11:01:16 INFO TaskSetManager: Finished task 0.0 in stage 0.0 (TID 0) in 1402 ms on ********** (executor 0) (1/2)
11:01:16  25/08/15 11:01:16 INFO PythonAccumulatorV2: Connected to AccumulatorServer at host: 127.0.0.1 port: 59625
11:01:16  25/08/15 11:01:16 INFO TaskSetManager: Finished task 1.0 in stage 0.0 (TID 1) in 1444 ms on ********** (executor 1) (2/2)
11:01:16  25/08/15 11:01:16 INFO TaskSchedulerImpl: Removed TaskSet 0.0, whose tasks have all completed, from pool
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: ResultStage 0 (json at NativeMethodAccessorImpl.java:0) finished in 1.622 s
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Job 0 is finished. Cancelling potential speculative or zombie tasks for this job
11:01:16  25/08/15 11:01:16 INFO TaskSchedulerImpl: Killing all running tasks in stage 0: Stage finished
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Job 0 finished: json at NativeMethodAccessorImpl.java:0, took 1.669387 s
11:01:16  25/08/15 11:01:16 INFO CodeGenerator: Code generated in 14.747677 ms
11:01:16  25/08/15 11:01:16 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Got job 1 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Final stage: ResultStage 1 (showString at NativeMethodAccessorImpl.java:0)
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Parents of final stage: List()
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Missing parents: List()
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Submitting ResultStage 1 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:16  25/08/15 11:01:16 INFO MemoryStore: Block broadcast_1 stored as values in memory (estimated size 25.4 KiB, free 434.3 MiB)
11:01:16  25/08/15 11:01:16 INFO MemoryStore: Block broadcast_1_piece0 stored as bytes in memory (estimated size 10.7 KiB, free 434.3 MiB)
11:01:16  25/08/15 11:01:16 INFO BlockManagerInfo: Added broadcast_1_piece0 in memory on ************:40201 (size: 10.7 KiB, free: 434.4 MiB)
11:01:16  25/08/15 11:01:16 INFO SparkContext: Created broadcast 1 from broadcast at DAGScheduler.scala:1585
11:01:16  25/08/15 11:01:16 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 1 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
11:01:16  25/08/15 11:01:16 INFO TaskSchedulerImpl: Adding task set 1.0 with 1 tasks resource profile 0
11:01:16  25/08/15 11:01:16 INFO TaskSetManager: Starting task 0.0 in stage 1.0 (TID 2) (**********, executor 1, partition 0, PROCESS_LOCAL, 11244 bytes)
11:01:16  25/08/15 11:01:16 INFO BlockManagerInfo: Added broadcast_1_piece0 in memory on **********:40201 (size: 10.7 KiB, free: 434.4 MiB)
11:01:17  25/08/15 11:01:17 INFO TaskSetManager: Finished task 0.0 in stage 1.0 (TID 2) in 269 ms on ********** (executor 1) (1/1)
11:01:17  25/08/15 11:01:17 INFO TaskSchedulerImpl: Removed TaskSet 1.0, whose tasks have all completed, from pool
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: ResultStage 1 (showString at NativeMethodAccessorImpl.java:0) finished in 0.283 s
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Job 1 is finished. Cancelling potential speculative or zombie tasks for this job
11:01:17  25/08/15 11:01:17 INFO TaskSchedulerImpl: Killing all running tasks in stage 1: Stage finished
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Job 1 finished: showString at NativeMethodAccessorImpl.java:0, took 0.290448 s
11:01:17  25/08/15 11:01:17 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Got job 2 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Final stage: ResultStage 2 (showString at NativeMethodAccessorImpl.java:0)
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Parents of final stage: List()
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Missing parents: List()
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Submitting ResultStage 2 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:17  25/08/15 11:01:17 INFO MemoryStore: Block broadcast_2 stored as values in memory (estimated size 25.4 KiB, free 434.3 MiB)
11:01:17  25/08/15 11:01:17 INFO MemoryStore: Block broadcast_2_piece0 stored as bytes in memory (estimated size 10.7 KiB, free 434.3 MiB)
11:01:17  25/08/15 11:01:17 INFO BlockManagerInfo: Added broadcast_2_piece0 in memory on ************:40201 (size: 10.7 KiB, free: 434.4 MiB)
11:01:17  25/08/15 11:01:17 INFO SparkContext: Created broadcast 2 from broadcast at DAGScheduler.scala:1585
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 2 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(1))
11:01:17  25/08/15 11:01:17 INFO TaskSchedulerImpl: Adding task set 2.0 with 1 tasks resource profile 0
11:01:17  25/08/15 11:01:17 INFO TaskSetManager: Starting task 0.0 in stage 2.0 (TID 3) (**********, executor 0, partition 1, PROCESS_LOCAL, 13231 bytes)
11:01:17  25/08/15 11:01:17 INFO BlockManagerInfo: Added broadcast_2_piece0 in memory on **********:40201 (size: 10.7 KiB, free: 434.4 MiB)
11:01:17  25/08/15 11:01:17 INFO TaskSetManager: Finished task 0.0 in stage 2.0 (TID 3) in 326 ms on ********** (executor 0) (1/1)
11:01:17  25/08/15 11:01:17 INFO TaskSchedulerImpl: Removed TaskSet 2.0, whose tasks have all completed, from pool
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: ResultStage 2 (showString at NativeMethodAccessorImpl.java:0) finished in 0.338 s
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Job 2 is finished. Cancelling potential speculative or zombie tasks for this job
11:01:17  25/08/15 11:01:17 INFO TaskSchedulerImpl: Killing all running tasks in stage 2: Stage finished
11:01:17  25/08/15 11:01:17 INFO DAGScheduler: Job 2 finished: showString at NativeMethodAccessorImpl.java:0, took 0.343680 s
11:01:17  25/08/15 11:01:17 INFO CodeGenerator: Code generated in 22.426009 ms
11:01:17  +-------------------+
11:01:17  |      key_as_string|
11:01:17  +-------------------+
11:01:17  |2025-08-12 11:00:00|
11:01:17  |2025-08-12 12:00:00|
11:01:17  |2025-08-12 13:00:00|
11:01:17  |2025-08-12 14:00:00|
11:01:17  |2025-08-12 15:00:00|
11:01:17  |2025-08-12 16:00:00|
11:01:17  |2025-08-12 17:00:00|
11:01:17  |2025-08-12 18:00:00|
11:01:17  |2025-08-12 19:00:00|
11:01:17  |2025-08-12 20:00:00|
11:01:17  |2025-08-12 21:00:00|
11:01:17  |2025-08-12 22:00:00|
11:01:17  |2025-08-12 23:00:00|
11:01:17  |2025-08-13 00:00:00|
11:01:17  |2025-08-13 01:00:00|
11:01:17  |2025-08-13 02:00:00|
11:01:17  |2025-08-13 03:00:00|
11:01:17  |2025-08-13 04:00:00|
11:01:17  |2025-08-13 05:00:00|
11:01:17  |2025-08-13 06:00:00|
11:01:17  +-------------------+
11:01:17  only showing top 20 rows
11:01:17
11:01:18  25/08/15 11:01:18 INFO CodeGenerator: Code generated in 129.139832 ms
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Registering RDD 15 (showString at NativeMethodAccessorImpl.java:0) as input to shuffle 0
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Got map stage job 3 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Final stage: ShuffleMapStage 3 (showString at NativeMethodAccessorImpl.java:0)
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Parents of final stage: List()
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Missing parents: List()
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Submitting ShuffleMapStage 3 (MapPartitionsRDD[15] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:18  25/08/15 11:01:18 INFO MemoryStore: Block broadcast_3 stored as values in memory (estimated size 50.0 KiB, free 434.2 MiB)
11:01:18  25/08/15 11:01:18 INFO MemoryStore: Block broadcast_3_piece0 stored as bytes in memory (estimated size 22.0 KiB, free 434.2 MiB)
11:01:18  25/08/15 11:01:18 INFO BlockManagerInfo: Added broadcast_3_piece0 in memory on ************:40201 (size: 22.0 KiB, free: 434.3 MiB)
11:01:18  25/08/15 11:01:18 INFO SparkContext: Created broadcast 3 from broadcast at DAGScheduler.scala:1585
11:01:18  25/08/15 11:01:18 INFO DAGScheduler: Submitting 1 missing tasks from ShuffleMapStage 3 (MapPartitionsRDD[15] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
11:01:18  25/08/15 11:01:18 INFO TaskSchedulerImpl: Adding task set 3.0 with 1 tasks resource profile 0
11:01:18  25/08/15 11:01:18 INFO TaskSetManager: Starting task 0.0 in stage 3.0 (TID 4) (**********, executor 1, partition 0, PROCESS_LOCAL, 11099 bytes)
11:01:18  25/08/15 11:01:18 INFO BlockManagerInfo: Added broadcast_3_piece0 in memory on **********:40201 (size: 22.0 KiB, free: 434.4 MiB)
11:01:43  25/08/15 11:01:43 INFO TaskSetManager: Finished task 0.0 in stage 3.0 (TID 4) in 24689 ms on ********** (executor 1) (1/1)
11:01:43  25/08/15 11:01:43 INFO TaskSchedulerImpl: Removed TaskSet 3.0, whose tasks have all completed, from pool
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: ShuffleMapStage 3 (showString at NativeMethodAccessorImpl.java:0) finished in 24.710 s
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: looking for newly runnable stages
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: running: Set()
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: waiting: Set()
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: failed: Set()
11:01:43  25/08/15 11:01:43 INFO ShufflePartitionsUtil: For shuffle(0), advisory target size: 67108864, actual target size 1048576, minimum partition size: 1048576
11:01:43  25/08/15 11:01:43 INFO HashAggregateExec: spark.sql.codegen.aggregate.map.twolevel.enabled is set to true, but current version of codegened fast hashmap does not support this aggregate.
11:01:43  25/08/15 11:01:43 INFO CodeGenerator: Code generated in 35.839711 ms
11:01:43  25/08/15 11:01:43 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Got job 4 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Final stage: ResultStage 5 (showString at NativeMethodAccessorImpl.java:0)
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Parents of final stage: List(ShuffleMapStage 4)
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Missing parents: List()
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Submitting ResultStage 5 (MapPartitionsRDD[18] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:43  25/08/15 11:01:43 INFO MemoryStore: Block broadcast_4 stored as values in memory (estimated size 53.6 KiB, free 434.2 MiB)
11:01:43  25/08/15 11:01:43 INFO MemoryStore: Block broadcast_4_piece0 stored as bytes in memory (estimated size 23.1 KiB, free 434.1 MiB)
11:01:43  25/08/15 11:01:43 INFO BlockManagerInfo: Added broadcast_4_piece0 in memory on ************:40201 (size: 23.1 KiB, free: 434.3 MiB)
11:01:43  25/08/15 11:01:43 INFO SparkContext: Created broadcast 4 from broadcast at DAGScheduler.scala:1585
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 5 (MapPartitionsRDD[18] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
11:01:43  25/08/15 11:01:43 INFO TaskSchedulerImpl: Adding task set 5.0 with 1 tasks resource profile 0
11:01:43  25/08/15 11:01:43 INFO TaskSetManager: Starting task 0.0 in stage 5.0 (TID 5) (**********, executor 1, partition 0, NODE_LOCAL, 11264 bytes)
11:01:43  25/08/15 11:01:43 INFO BlockManagerInfo: Added broadcast_4_piece0 in memory on **********:40201 (size: 23.1 KiB, free: 434.3 MiB)
11:01:43  25/08/15 11:01:43 INFO MapOutputTrackerMasterEndpoint: Asked to send map output locations for shuffle 0 to 2.49.0.0:25063
11:01:43  25/08/15 11:01:43 INFO TaskSetManager: Finished task 0.0 in stage 5.0 (TID 5) in 210 ms on ********** (executor 1) (1/1)
11:01:43  25/08/15 11:01:43 INFO TaskSchedulerImpl: Removed TaskSet 5.0, whose tasks have all completed, from pool
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: ResultStage 5 (showString at NativeMethodAccessorImpl.java:0) finished in 0.222 s
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Job 4 is finished. Cancelling potential speculative or zombie tasks for this job
11:01:43  25/08/15 11:01:43 INFO TaskSchedulerImpl: Killing all running tasks in stage 5: Stage finished
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Job 4 finished: showString at NativeMethodAccessorImpl.java:0, took 0.237319 s
11:01:43  25/08/15 11:01:43 INFO CodeGenerator: Code generated in 12.16551 ms
11:01:43  +----------+-----------+-----+-------------------+
11:01:43  |news_count|wxpyq_count|total|               hour|
11:01:43  +----------+-----------+-----+-------------------+
11:01:43  |        47|          0|   47|2025-08-14 19:00:00|
11:01:43  |         9|          0|    9|2025-08-14 20:00:00|
11:01:43  |        41|          0|   41|2025-08-13 21:00:00|
11:01:43  |         0|          0|    0|2025-08-12 18:00:00|
11:01:43  |        66|          0|   66|2025-08-15 02:00:00|
11:01:43  |         4|          0|    4|2025-08-14 06:00:00|
11:01:43  |        36|          0|   36|2025-08-14 01:00:00|
11:01:43  |        23|          0|   23|2025-08-12 13:00:00|
11:01:43  |         9|          0|    9|2025-08-15 10:00:00|
11:01:43  |         4|          0|    4|2025-08-15 09:00:00|
11:01:43  +----------+-----------+-----+-------------------+
11:01:43  only showing top 10 rows
11:01:43
11:01:43  === 调试：data_process_count表中的最新时间 ===
11:01:43  25/08/15 11:01:43 INFO CodeGenerator: Code generated in 14.624576 ms
11:01:43  25/08/15 11:01:43 INFO CodeGenerator: Code generated in 13.209891 ms
11:01:43  25/08/15 11:01:43 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Got job 5 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Final stage: ResultStage 6 (showString at NativeMethodAccessorImpl.java:0)
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Parents of final stage: List()
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Missing parents: List()
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Submitting ResultStage 6 (MapPartitionsRDD[22] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:43  25/08/15 11:01:43 INFO MemoryStore: Block broadcast_5 stored as values in memory (estimated size 15.7 KiB, free 434.1 MiB)
11:01:43  25/08/15 11:01:43 INFO MemoryStore: Block broadcast_5_piece0 stored as bytes in memory (estimated size 7.7 KiB, free 434.1 MiB)
11:01:43  25/08/15 11:01:43 INFO BlockManagerInfo: Added broadcast_5_piece0 in memory on ************:40201 (size: 7.7 KiB, free: 434.3 MiB)
11:01:43  25/08/15 11:01:43 INFO SparkContext: Created broadcast 5 from broadcast at DAGScheduler.scala:1585
11:01:43  25/08/15 11:01:43 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 6 (MapPartitionsRDD[22] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
11:01:43  25/08/15 11:01:43 INFO TaskSchedulerImpl: Adding task set 6.0 with 1 tasks resource profile 0
11:01:43  25/08/15 11:01:43 INFO TaskSetManager: Starting task 0.0 in stage 6.0 (TID 6) (**********, executor 1, partition 0, PROCESS_LOCAL, 11110 bytes)
11:01:43  25/08/15 11:01:43 INFO BlockManagerInfo: Added broadcast_5_piece0 in memory on **********:40201 (size: 7.7 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Finished task 0.0 in stage 6.0 (TID 6) in 212 ms on ********** (executor 1) (1/1)
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Removed TaskSet 6.0, whose tasks have all completed, from pool
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: ResultStage 6 (showString at NativeMethodAccessorImpl.java:0) finished in 0.221 s
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Job 5 is finished. Cancelling potential speculative or zombie tasks for this job
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Killing all running tasks in stage 6: Stage finished
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Job 5 finished: showString at NativeMethodAccessorImpl.java:0, took 0.225366 s
11:01:44  25/08/15 11:01:44 INFO CodeGenerator: Code generated in 8.653312 ms
11:01:44  25/08/15 11:01:44 INFO CodeGenerator: Code generated in 12.385601 ms
11:01:44  +------------------+-------------------+
11:01:44  |     original_hour|     converted_time|
11:01:44  +------------------+-------------------+
11:01:44  |2025年08月15日09时|2025-08-15 09:00:00|
11:01:44  |2025年08月15日09时|2025-08-15 09:00:00|
11:01:44  |2025年08月15日08时|2025-08-15 08:00:00|
11:01:44  |2025年08月15日08时|2025-08-15 08:00:00|
11:01:44  |2025年08月15日07时|2025-08-15 07:00:00|
11:01:44  +------------------+-------------------+
11:01:44
11:01:44  25/08/15 11:01:44 INFO CodeGenerator: Code generated in 8.027445 ms
11:01:44  25/08/15 11:01:44 INFO CodeGenerator: Code generated in 10.867405 ms
11:01:44  25/08/15 11:01:44 INFO SparkContext: Starting job: $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Got job 6 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) with 1 output partitions
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Final stage: ResultStage 7 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264)
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Parents of final stage: List()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Missing parents: List()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Submitting ResultStage 7 (MapPartitionsRDD[27] at $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264), which has no missing parents
11:01:44  25/08/15 11:01:44 INFO MemoryStore: Block broadcast_6 stored as values in memory (estimated size 28.5 KiB, free 434.1 MiB)
11:01:44  25/08/15 11:01:44 INFO MemoryStore: Block broadcast_6_piece0 stored as bytes in memory (estimated size 13.6 KiB, free 434.1 MiB)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_6_piece0 in memory on ************:40201 (size: 13.6 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO SparkContext: Created broadcast 6 from broadcast at DAGScheduler.scala:1585
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 7 (MapPartitionsRDD[27] at $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) (first 15 tasks are for partitions Vector(0))
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Adding task set 7.0 with 1 tasks resource profile 0
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Starting task 0.0 in stage 7.0 (TID 7) (**********, executor 1, partition 0, PROCESS_LOCAL, 11110 bytes)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_6_piece0 in memory on **********:40201 (size: 13.6 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Finished task 0.0 in stage 7.0 (TID 7) in 119 ms on ********** (executor 1) (1/1)
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Removed TaskSet 7.0, whose tasks have all completed, from pool
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: ResultStage 7 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) finished in 0.130 s
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Job 6 is finished. Cancelling potential speculative or zombie tasks for this job
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Killing all running tasks in stage 7: Stage finished
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Job 6 finished: $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264, took 0.134444 s
11:01:44  25/08/15 11:01:44 INFO CodeGenerator: Code generated in 9.079686 ms
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Registering RDD 29 (showString at NativeMethodAccessorImpl.java:0) as input to shuffle 1
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Got map stage job 7 (showString at NativeMethodAccessorImpl.java:0) with 2 output partitions
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Final stage: ShuffleMapStage 8 (showString at NativeMethodAccessorImpl.java:0)
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Parents of final stage: List()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Missing parents: List()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Submitting ShuffleMapStage 8 (MapPartitionsRDD[29] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:44  25/08/15 11:01:44 INFO MemoryStore: Block broadcast_7 stored as values in memory (estimated size 27.9 KiB, free 434.1 MiB)
11:01:44  25/08/15 11:01:44 INFO MemoryStore: Block broadcast_7_piece0 stored as bytes in memory (estimated size 11.9 KiB, free 434.0 MiB)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_7_piece0 in memory on ************:40201 (size: 11.9 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO SparkContext: Created broadcast 7 from broadcast at DAGScheduler.scala:1585
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Submitting 2 missing tasks from ShuffleMapStage 8 (MapPartitionsRDD[29] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0, 1))
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Adding task set 8.0 with 2 tasks resource profile 0
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Starting task 0.0 in stage 8.0 (TID 8) (**********, executor 0, partition 0, PROCESS_LOCAL, 11233 bytes)
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Starting task 1.0 in stage 8.0 (TID 9) (**********, executor 1, partition 1, PROCESS_LOCAL, 13220 bytes)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_7_piece0 in memory on **********:40201 (size: 11.9 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_7_piece0 in memory on **********:40201 (size: 11.9 KiB, free: 434.4 MiB)
11:01:44  25/08/15 11:01:44 INFO CodeGenerator: Code generated in 56.128106 ms
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Registering RDD 31 (showString at NativeMethodAccessorImpl.java:0) as input to shuffle 2
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Got map stage job 8 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Final stage: ShuffleMapStage 9 (showString at NativeMethodAccessorImpl.java:0)
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Parents of final stage: List()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Missing parents: List()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Submitting ShuffleMapStage 9 (MapPartitionsRDD[31] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:01:44  25/08/15 11:01:44 INFO MemoryStore: Block broadcast_8 stored as values in memory (estimated size 61.5 KiB, free 434.0 MiB)
11:01:44  25/08/15 11:01:44 INFO MemoryStore: Block broadcast_8_piece0 stored as bytes in memory (estimated size 26.5 KiB, free 434.0 MiB)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_8_piece0 in memory on ************:40201 (size: 26.5 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO SparkContext: Created broadcast 8 from broadcast at DAGScheduler.scala:1585
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: Submitting 1 missing tasks from ShuffleMapStage 9 (MapPartitionsRDD[31] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Adding task set 9.0 with 1 tasks resource profile 0
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Starting task 0.0 in stage 9.0 (TID 10) (**********, executor 0, partition 0, PROCESS_LOCAL, 11099 bytes)
11:01:44  25/08/15 11:01:44 INFO BlockManagerInfo: Added broadcast_8_piece0 in memory on **********:40201 (size: 26.5 KiB, free: 434.3 MiB)
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Finished task 1.0 in stage 8.0 (TID 9) in 132 ms on ********** (executor 1) (1/2)
11:01:44  25/08/15 11:01:44 INFO TaskSetManager: Finished task 0.0 in stage 8.0 (TID 8) in 256 ms on ********** (executor 0) (2/2)
11:01:44  25/08/15 11:01:44 INFO TaskSchedulerImpl: Removed TaskSet 8.0, whose tasks have all completed, from pool
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: ShuffleMapStage 8 (showString at NativeMethodAccessorImpl.java:0) finished in 0.264 s
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: looking for newly runnable stages
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: running: Set(ShuffleMapStage 9)
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: waiting: Set()
11:01:44  25/08/15 11:01:44 INFO DAGScheduler: failed: Set()
11:02:11  25/08/15 11:02:11 INFO TaskSetManager: Finished task 0.0 in stage 9.0 (TID 10) in 26725 ms on ********** (executor 0) (1/1)
11:02:11  25/08/15 11:02:11 INFO TaskSchedulerImpl: Removed TaskSet 9.0, whose tasks have all completed, from pool
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: ShuffleMapStage 9 (showString at NativeMethodAccessorImpl.java:0) finished in 26.734 s
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: looking for newly runnable stages
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: running: Set()
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: waiting: Set()
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: failed: Set()
11:02:11  25/08/15 11:02:11 INFO ShufflePartitionsUtil: For shuffle(1, 2), advisory target size: 67108864, actual target size 1048576, minimum partition size: 1048576
11:02:11  25/08/15 11:02:11 INFO CodeGenerator: Code generated in 7.237264 ms
11:02:11  25/08/15 11:02:11 INFO CodeGenerator: Code generated in 29.895944 ms
11:02:11  25/08/15 11:02:11 INFO CodeGenerator: Code generated in 10.484167 ms
11:02:11  25/08/15 11:02:11 INFO HashAggregateExec: spark.sql.codegen.aggregate.map.twolevel.enabled is set to true, but current version of codegened fast hashmap does not support this aggregate.
11:02:11  25/08/15 11:02:11 INFO CodeGenerator: Code generated in 24.409469 ms
11:02:11  25/08/15 11:02:11 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: Got job 9 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: Final stage: ResultStage 12 (showString at NativeMethodAccessorImpl.java:0)
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: Parents of final stage: List(ShuffleMapStage 10, ShuffleMapStage 11)
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: Missing parents: List()
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: Submitting ResultStage 12 (MapPartitionsRDD[39] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
11:02:11  25/08/15 11:02:11 INFO MemoryStore: Block broadcast_9 stored as values in memory (estimated size 105.9 KiB, free 433.9 MiB)
11:02:11  25/08/15 11:02:11 INFO MemoryStore: Block broadcast_9_piece0 stored as bytes in memory (estimated size 44.8 KiB, free 433.8 MiB)
11:02:11  25/08/15 11:02:11 INFO BlockManagerInfo: Added broadcast_9_piece0 in memory on ************:40201 (size: 44.8 KiB, free: 434.2 MiB)
11:02:11  25/08/15 11:02:11 INFO SparkContext: Created broadcast 9 from broadcast at DAGScheduler.scala:1585
11:02:11  25/08/15 11:02:11 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 12 (MapPartitionsRDD[39] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
11:02:11  25/08/15 11:02:11 INFO TaskSchedulerImpl: Adding task set 12.0 with 1 tasks resource profile 0
11:02:11  25/08/15 11:02:11 INFO TaskSetManager: Starting task 0.0 in stage 12.0 (TID 11) (**********, executor 1, partition 0, NODE_LOCAL, 11546 bytes)
11:02:11  25/08/15 11:02:11 INFO BlockManagerInfo: Added broadcast_9_piece0 in memory on **********:40201 (size: 44.8 KiB, free: 434.3 MiB)
11:02:11  25/08/15 11:02:11 INFO MapOutputTrackerMasterEndpoint: Asked to send map output locations for shuffle 1 to 2.49.0.0:25063
11:02:11  25/08/15 11:02:11 INFO MapOutputTrackerMasterEndpoint: Asked to send map output locations for shuffle 2 to 2.49.0.0:25063
11:02:12  25/08/15 11:02:12 INFO TaskSetManager: Finished task 0.0 in stage 12.0 (TID 11) in 311 ms on ********** (executor 1) (1/1)
11:02:12  25/08/15 11:02:12 INFO TaskSchedulerImpl: Removed TaskSet 12.0, whose tasks have all completed, from pool
11:02:12  25/08/15 11:02:12 INFO DAGScheduler: ResultStage 12 (showString at NativeMethodAccessorImpl.java:0) finished in 0.323 s
11:02:12  25/08/15 11:02:12 INFO DAGScheduler: Job 9 is finished. Cancelling potential speculative or zombie tasks for this job
11:02:12  25/08/15 11:02:12 INFO TaskSchedulerImpl: Killing all running tasks in stage 12: Stage finished
11:02:12  25/08/15 11:02:12 INFO DAGScheduler: Job 9 finished: showString at NativeMethodAccessorImpl.java:0, took 0.330509 s
11:02:12  25/08/15 11:02:12 INFO CodeGenerator: Code generated in 11.479366 ms
11:02:12  25/08/15 11:02:12 INFO CodeGenerator: Code generated in 11.545447 ms
11:02:12  +----------+-----------+-----+------------------+--------------+----------+----+--------------------+------+
11:02:12  |news_count|wxpyq_count|total|              hour|           day|     month|year|         insert_time|source|
11:02:12  +----------+-----------+-----+------------------+--------------+----------+----+--------------------+------+
11:02:12  |         4|          0|    4|2025年08月15日09时|2025年08月15日|2025年08月|2025|2025-08-15 11:01:...| trslc|
11:02:12  |         9|          0|    9|2025年08月15日10时|2025年08月15日|2025年08月|2025|2025-08-15 11:01:...| trslc|
11:02:12  +----------+-----------+-----+------------------+--------------+----------+----+--------------------+------+
11:02:12
11:02:12  25/08/15 11:02:12 INFO SparkContext: SparkContext is stopping with exitCode 0.
11:02:12  25/08/15 11:02:12 INFO SparkUI: Stopped Spark web UI at http://************:4041
11:02:12  25/08/15 11:02:12 INFO StandaloneSchedulerBackend: Shutting down all executors
11:02:12  25/08/15 11:02:12 INFO StandaloneSchedulerBackend$StandaloneDriverEndpoint: Asking each executor to shut down
11:02:12  25/08/15 11:02:12 INFO MapOutputTrackerMasterEndpoint: MapOutputTrackerMasterEndpoint stopped!
11:02:12  25/08/15 11:02:12 INFO MemoryStore: MemoryStore cleared
11:02:12  25/08/15 11:02:12 INFO BlockManager: BlockManager stopped
11:02:12  25/08/15 11:02:12 INFO BlockManagerMaster: BlockManagerMaster stopped
11:02:12  25/08/15 11:02:12 INFO OutputCommitCoordinator$OutputCommitCoordinatorEndpoint: OutputCommitCoordinator stopped!
11:02:12  25/08/15 11:02:12 INFO SparkContext: Successfully stopped SparkContext
11:02:13  25/08/15 11:02:13 INFO ShutdownHookManager: Shutdown hook called
11:02:13  25/08/15 11:02:13 INFO ShutdownHookManager: Deleting directory /tmp/spark-347ef113-1130-4d82-bf80-16cef44bae89
11:02:13  25/08/15 11:02:13 INFO ShutdownHookManager: Deleting directory /tmp/spark-05a12ba1-5a81-45b2-bd2b-582126b6aa12
11:02:13  25/08/15 11:02:13 INFO ShutdownHookManager: Deleting directory /tmp/spark-347ef113-1130-4d82-bf80-16cef44bae89/pyspark-c5b33fcc-93af-40ff-9a5c-6ef7caa05e64
11:02:13  执行任务结束：任务01
==================================================