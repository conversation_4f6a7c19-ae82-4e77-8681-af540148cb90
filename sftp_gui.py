# -*- coding: utf-8 -*-
import os
import threading
import queue
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import logging

# 导入同目录的下载模块
try:
    import 文件助手 as downloader  # noqa: F401
except Exception as e:
    downloader = None  # type: ignore
    _import_error = e
else:
    _import_error = None


class TkQueueHandler(logging.Handler):
    def __init__(self, q: queue.Queue):
        super().__init__()
        self.q = q

    def emit(self, record: logging.LogRecord) -> None:
        try:
            msg = self.format(record)
        except Exception:
            msg = record.getMessage()
        try:
            self.q.put_nowait(msg)
        except Exception:
            pass


class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("SFTP 并发下载器")
        self.geometry("820x640")
        self.resizable(True, True)

        self.msg_q: queue.Queue[str] = queue.Queue()
        self._worker: threading.Thread | None = None
        self._running = False

        self._build_ui()
        self._setup_logger()
        self._poll_msgs()

        if _import_error is not None:
            messagebox.showerror(
                "依赖缺失",
                f"导入模块失败: {_import_error}\n\n请先在虚拟环境中安装依赖: pip install paramiko",
            )

    def _build_ui(self):
        pad = {"padx": 6, "pady": 4}
        frm = ttk.Frame(self)
        frm.pack(fill=tk.BOTH, expand=True)

        # 连接参数
        g_conn = ttk.LabelFrame(frm, text="连接")
        g_conn.pack(fill=tk.X, **pad)
        self.var_host = tk.StringVar()
        self.var_port = tk.IntVar(value=22)
        self.var_user = tk.StringVar()
        self.var_pass = tk.StringVar()
        self.var_pkey = tk.StringVar()
        self.var_pkey_pass = tk.StringVar()

        row = 0
        ttk.Label(g_conn, text="Host").grid(row=row, column=0, sticky=tk.E, **pad)
        ttk.Entry(g_conn, textvariable=self.var_host, width=28).grid(row=row, column=1, **pad)
        ttk.Label(g_conn, text="Port").grid(row=row, column=2, sticky=tk.E, **pad)
        ttk.Spinbox(g_conn, from_=1, to=65535, textvariable=self.var_port, width=8).grid(row=row, column=3, **pad)
        row += 1
        ttk.Label(g_conn, text="User").grid(row=row, column=0, sticky=tk.E, **pad)
        ttk.Entry(g_conn, textvariable=self.var_user, width=28).grid(row=row, column=1, **pad)
        ttk.Label(g_conn, text="Password").grid(row=row, column=2, sticky=tk.E, **pad)
        ttk.Entry(g_conn, textvariable=self.var_pass, width=20, show="*").grid(row=row, column=3, **pad)
        row += 1
        ttk.Label(g_conn, text="私钥").grid(row=row, column=0, sticky=tk.E, **pad)
        e_pkey = ttk.Entry(g_conn, textvariable=self.var_pkey, width=48)
        e_pkey.grid(row=row, column=1, columnspan=2, sticky=tk.W + tk.E, **pad)
        ttk.Button(g_conn, text="选择...", command=self._choose_pkey).grid(row=row, column=3, **pad)
        row += 1
        ttk.Label(g_conn, text="口令").grid(row=row, column=0, sticky=tk.E, **pad)
        ttk.Entry(g_conn, textvariable=self.var_pkey_pass, width=20, show="*").grid(row=row, column=1, **pad)

        # 路径与匹配
        g_path = ttk.LabelFrame(frm, text="路径与匹配")
        g_path.pack(fill=tk.X, **pad)
        self.var_remote = tk.StringVar()
        self.var_local = tk.StringVar(value=os.path.abspath("downloads"))
        self.var_pattern = tk.StringVar(value="*.csv")
        self.var_ptype = tk.StringVar(value="glob")
        self.var_ignore_case = tk.BooleanVar(value=False)
        self.var_recursive = tk.BooleanVar(value=True)
        self.var_force = tk.BooleanVar(value=False)

        row = 0
        ttk.Label(g_path, text="远程目录").grid(row=row, column=0, sticky=tk.E, **pad)
        ttk.Entry(g_path, textvariable=self.var_remote, width=56).grid(row=row, column=1, columnspan=2, sticky=tk.W + tk.E, **pad)
        row += 1
        ttk.Label(g_path, text="本地目录").grid(row=row, column=0, sticky=tk.E, **pad)
        ttk.Entry(g_path, textvariable=self.var_local, width=56).grid(row=row, column=1, sticky=tk.W + tk.E, **pad)
        ttk.Button(g_path, text="浏览...", command=self._choose_local).grid(row=row, column=2, **pad)
        row += 1
        ttk.Label(g_path, text="匹配模式").grid(row=row, column=0, sticky=tk.E, **pad)
        ttk.Entry(g_path, textvariable=self.var_pattern, width=40).grid(row=row, column=1, sticky=tk.W, **pad)
        ttk.Label(g_path, text="类型").grid(row=row, column=2, sticky=tk.E, **pad)
        ttk.Combobox(g_path, values=["glob", "substr", "regex"], textvariable=self.var_ptype, width=8, state="readonly").grid(row=row, column=3, **pad)
        row += 1
        ttk.Checkbutton(g_path, text="忽略大小写", variable=self.var_ignore_case).grid(row=row, column=1, sticky=tk.W, **pad)
        ttk.Checkbutton(g_path, text="递归子目录", variable=self.var_recursive).grid(row=row, column=2, sticky=tk.W, **pad)
        ttk.Checkbutton(g_path, text="强制覆盖", variable=self.var_force).grid(row=row, column=3, sticky=tk.W, **pad)

        # 性能与日志
        g_run = ttk.LabelFrame(frm, text="运行")
        g_run.pack(fill=tk.X, **pad)
        self.var_concurrency = tk.IntVar(value=8)
        self.var_verbose = tk.IntVar(value=1)
        ttk.Label(g_run, text="并发").grid(row=0, column=0, sticky=tk.E, **pad)
        ttk.Spinbox(g_run, from_=1, to=64, textvariable=self.var_concurrency, width=6).grid(row=0, column=1, **pad)
        ttk.Label(g_run, text="日志").grid(row=0, column=2, sticky=tk.E, **pad)
        ttk.Combobox(g_run, values=[0, 1, 2], textvariable=self.var_verbose, width=6, state="readonly").grid(row=0, column=3, **pad)
        self.btn_start = ttk.Button(g_run, text="开始下载", command=self._on_start)
        self.btn_start.grid(row=0, column=4, padx=10)
        self.pb = ttk.Progressbar(g_run, mode="indeterminate", length=220)
        self.pb.grid(row=0, column=5, padx=10)

        # 日志输出
        g_log = ttk.LabelFrame(frm, text="日志")
        g_log.pack(fill=tk.BOTH, expand=True, **pad)
        self.txt = tk.Text(g_log, height=20, wrap=tk.NONE)
        self.txt.pack(fill=tk.BOTH, expand=True)
        self.txt.configure(state=tk.DISABLED)

    def _setup_logger(self):
        self.gui_logger = logging.getLogger("sftp_gui")
        self.gui_logger.setLevel(logging.DEBUG)
        handler = TkQueueHandler(self.msg_q)
        fmt = logging.Formatter("[%(asctime)s] %(levelname)s: %(message)s", datefmt="%H:%M:%S")
        handler.setFormatter(fmt)
        self.gui_logger.handlers.clear()
        self.gui_logger.addHandler(handler)

    def _append_text(self, s: str):
        self.txt.configure(state=tk.NORMAL)
        self.txt.insert(tk.END, s + "\n")
        self.txt.see(tk.END)
        self.txt.configure(state=tk.DISABLED)

    def _poll_msgs(self):
        try:
            while True:
                msg = self.msg_q.get_nowait()
                self._append_text(msg)
        except queue.Empty:
            pass
        self.after(80, self._poll_msgs)

    def _choose_local(self):
        d = filedialog.askdirectory(title="选择本地目录", initialdir=self.var_local.get() or os.getcwd())
        if d:
            self.var_local.set(d)

    def _choose_pkey(self):
        f = filedialog.askopenfilename(title="选择私钥文件", initialdir=os.path.expanduser("~"))
        if f:
            self.var_pkey.set(f)

    def _on_start(self):
        if _import_error is not None or downloader is None:
            messagebox.showerror("依赖缺失", f"无法启动: {_import_error}")
            return
        if self._running:
            return
        # 参数校验
        if not self.var_host.get().strip():
            messagebox.showwarning("提示", "请填写 Host")
            return
        if not self.var_user.get().strip():
            messagebox.showwarning("提示", "请填写 User")
            return
        if not self.var_remote.get().strip():
            messagebox.showwarning("提示", "请填写远程目录")
            return
        if not self.var_local.get().strip():
            messagebox.showwarning("提示", "请填写本地目录")
            return

        self._running = True
        self.btn_start.configure(state=tk.DISABLED)
        self.pb.start(80)

        t = threading.Thread(target=self._run_task, daemon=True)
        t.start()
        self._worker = t

    def _build_logger_for_task(self):
        # 使用 GUI 的 handler，将 downloader 的日志写入文本框
        logger = logging.getLogger("sftp_downloader_gui")
        logger.setLevel(logging.DEBUG)
        handler = TkQueueHandler(self.msg_q)
        fmt = logging.Formatter("[%(asctime)s] %(levelname)s: %(message)s", datefmt="%H:%M:%S")
        handler.setFormatter(fmt)
        logger.handlers.clear()
        # 设置由界面选择的日志级别
        v = int(self.var_verbose.get())
        if v <= 0:
            handler.setLevel(logging.WARNING)
        elif v == 1:
            handler.setLevel(logging.INFO)
        else:
            handler.setLevel(logging.DEBUG)
        logger.addHandler(handler)
        return logger

    def _run_task(self):
        try:
            cfg = downloader.SFTPConfig(
                host=self.var_host.get().strip(),
                port=int(self.var_port.get()),
                username=self.var_user.get().strip(),
                password=(self.var_pass.get() or None),
                key_filename=(self.var_pkey.get() or None),
                passphrase=(self.var_pkey_pass.get() or None),
            )
            patterns_raw = [p.strip() for p in self.var_pattern.get().split(",") if p.strip()]
            if not patterns_raw:
                patterns_raw = ["*"] if self.var_ptype.get() == "glob" else [""]
            logger = self._build_logger_for_task()
            os.makedirs(self.var_local.get(), exist_ok=True)
            result = downloader.run_download(
                cfg=cfg,
                remote_dir=self.var_remote.get().strip(),
                local_dir=self.var_local.get().strip(),
                patterns=patterns_raw,
                pattern_type=self.var_ptype.get(),
                ignore_case=bool(self.var_ignore_case.get()),
                concurrency=max(1, int(self.var_concurrency.get())),
                recursive=bool(self.var_recursive.get()),
                skip_existing=not bool(self.var_force.get()),
                logger=logger,
            )
            messagebox.showinfo(
                "完成",
                f"下载: {result.get('downloaded',0)}\n跳过: {result.get('skipped',0)}\n失败: {result.get('failed',0)}\n耗时: {result.get('elapsed',0):.2f}s",
            )
        except Exception as e:
            messagebox.showerror("错误", f"执行失败: {e}")
        finally:
            self._running = False
            self.pb.stop()
            self.btn_start.configure(state=tk.NORMAL)


if __name__ == "__main__":
    app = App()
    app.mainloop()

