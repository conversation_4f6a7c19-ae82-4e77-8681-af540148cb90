# Protobuf错误深度解决方案

## 问题分析

您遇到的 `google.protobuf.message.DecodeError: Unexpected end-group tag.` 错误是一个比较严重的protobuf解析错误，通常表示：

1. **数据传输中断**：网络不稳定导致protobuf数据包不完整
2. **服务端数据异常**：Hybase服务返回了格式错误的protobuf数据
3. **连接状态异常**：长连接状态异常导致数据解析失败
4. **并发冲突**：多个查询同时使用同一连接导致状态混乱

## 新的解决策略

### 1. 连接重建策略
每次查询都重新建立连接，避免连接状态异常：
```python
def safe_category_query_with_reconnect(table_name, where, group_field, max_results=50000, max_retries=5):
    # 每次重试都重新建立连接
    conn = create_hybase_connection(host, port, username, password)
```

### 2. 智能重试机制
- **重试次数**：增加到5次
- **等待时间**：递增等待（5秒、10秒、15秒、20秒、25秒）
- **错误分类**：特殊处理protobuf错误

### 3. 问题表管理
自动识别和跳过持续出现问题的表：
```python
# 连续3次protobuf错误后自动跳过该表
if consecutive_protobuf_errors >= 3:
    add_problematic_table(table_name)
```

### 4. 数据量控制
减少单次查询的数据量，降低出错概率：
- 原来：1,000,000 条记录
- 现在：50,000 条记录

### 5. 查询条件清理
清理可能导致问题的查询条件：
```python
def clean_query_condition(where):
    # 移除多余空格，确保格式正确
    return ' '.join(where.split()).strip()
```

## 使用方法

### 1. 直接运行修复后的脚本
```bash
# 运行主程序
python 西藏2.py
```

### 2. 运行测试验证
```bash
# 运行测试脚本
python test_fix.py
```

### 3. 监控日志输出
关注以下日志信息：
- `检测到protobuf解析错误`
- `表 XXX 已添加到问题表列表中`
- `跳过问题表 XXX`

## 应急处理方案

### 方案1：手动跳过问题表
如果某个表持续出现问题，可以手动添加到跳过列表：
```python
# 在脚本开头添加
PROBLEMATIC_TABLES.add("problematic_table_name")
```

### 方案2：分批处理
将时间范围分成更小的批次：
```python
# 修改时间分割粒度
def split_time_by_hour(begin_time, end_time):
    # 可以改为30分钟或15分钟间隔
    pass
```

### 方案3：降级处理
如果问题持续，可以临时使用空数据：
```python
# 所有查询都返回空结果，但保持程序运行
def emergency_mode():
    return create_empty_rows(time_range)
```

## 预防措施

### 1. 网络优化
- 确保网络连接稳定
- 考虑使用专线或VPN
- 增加网络超时设置

### 2. 服务端检查
- 检查Hybase服务状态
- 查看服务端日志
- 确认数据完整性

### 3. 资源监控
- 监控内存使用情况
- 检查磁盘空间
- 观察CPU负载

## 长期解决方案

### 1. 升级依赖
考虑升级相关组件：
- protobuf库版本
- hybaseapi版本
- Python版本

### 2. 架构优化
- 考虑使用连接池
- 实现查询队列
- 添加熔断机制

### 3. 监控告警
- 设置错误率告警
- 监控查询延迟
- 跟踪成功率指标

## 故障排查步骤

1. **检查网络连接**
   ```bash
   ping 10.83.13.75
   telnet 10.83.13.75 5555
   ```

2. **查看服务状态**
   ```bash
   # 检查Hybase服务状态
   curl http://10.83.13.75:5555/health
   ```

3. **分析错误模式**
   - 是否特定表出错？
   - 是否特定时间段出错？
   - 错误是否有规律？

4. **逐步排查**
   - 先测试单个表
   - 减少查询条件
   - 缩小时间范围

## 成功指标

修复成功的标志：
- ✅ 程序能够完整运行不崩溃
- ✅ 大部分表能够正常查询
- ✅ 即使部分表失败，整体任务仍能完成
- ✅ 错误日志清晰，便于问题定位

## 注意事项

1. **数据完整性**：部分表跳过可能影响数据完整性
2. **性能影响**：重新建立连接会增加查询时间
3. **资源消耗**：更多重试会增加系统负载
4. **监控需求**：需要密切关注日志和错误率
