from datetime import datetime, timedelta
from pyspark.sql import SparkSession, DataFrame
import requests

# 返回空的 DataFrame
spark = SparkSession.builder.appName("spark-app-save-count").getOrCreate()

def create_table_rows():
    last_max_time_str = get_last_max_time_str()
    print(f"DEBUG: 上一次最大时间字符串：{last_max_time_str}")

    all_table_df = spark.sql(f"""
select default_time_df.key_as_string                                                           as key_as_string,
       (CASE WHEN weibo_df.doc_count is null THEN 0 ELSE weibo_df.doc_count END)               as weibo_count,
       (CASE WHEN app_df.doc_count is null THEN 0 ELSE app_df.doc_count END)                   as app_count,
       (CASE WHEN blog_df.doc_count is null THEN 0 ELSE blog_df.doc_count END)                 as blog_count,
       (CASE WHEN wechat_df.doc_count is null THEN 0 ELSE wechat_df.doc_count END)             as wechat_count,
       (CASE WHEN zimeiti_df.doc_count is null THEN 0 ELSE zimeiti_df.doc_count END)           as zimeiti_count,
       (CASE WHEN foreign_news_df.doc_count is null THEN 0 ELSE foreign_news_df.doc_count END) as foreign_news_count,
       (CASE WHEN tianji_luntan_df.doc_count is null THEN 0 ELSE tianji_luntan_df.doc_count END) as tianji_luntan_count,
       (CASE WHEN news_df.doc_count is null THEN 0 ELSE news_df.doc_count END)                 as news_count,
       (CASE WHEN facebook_df.doc_count is null THEN 0 ELSE facebook_df.doc_count END)         as facebook_count,
       (CASE WHEN tianji_luntan_comment_df.doc_count is null THEN 0 ELSE tianji_luntan_comment_df.doc_count END) as tianji_luntan_comment_count,
       (CASE WHEN tianji_zhihu_df.doc_count is null THEN 0 ELSE tianji_zhihu_df.doc_count END) as tianji_zhihu_count,
       (CASE WHEN twitter_df.doc_count is null THEN 0 ELSE twitter_df.doc_count END)           as twitter_count,
       (CASE WHEN tianji_zhihu_comment_df.doc_count is null THEN 0 ELSE tianji_zhihu_comment_df.doc_count END) as tianji_zhihu_comment_count,
       (CASE WHEN yangban_news_comment_df.doc_count is null THEN 0 ELSE yangban_news_comment_df.doc_count END) as yangban_news_comment_count,
       (weibo_count + app_count + blog_count + wechat_count + zimeiti_count + 
       foreign_news_count + tianji_luntan_count + news_count + facebook_count +
       tianji_luntan_comment_count + tianji_zhihu_count + twitter_count + 
       tianji_zhihu_comment_count + yangban_news_comment_count) as total_count,
       'yb' as g_asp,
        DATE_FORMAT(to_timestamp(default_time_df.key_as_string, 'yyyy-MM-dd HH:mm:ss'), 'yyyy年MM月dd日HH时') AS hour,
        DATE_FORMAT(to_timestamp(default_time_df.key_as_string, 'yyyy-MM-dd HH:mm:ss'), 'yyyy年MM月dd日') AS day,
        DATE_FORMAT(to_timestamp(default_time_df.key_as_string, 'yyyy-MM-dd HH:mm:ss'), 'yyyy年MM月') AS month,
        YEAR(to_timestamp(default_time_df.key_as_string, 'yyyy-MM-dd HH:mm:ss')) AS year,
        to_timestamp(hour, 'yyyy年MM月dd日HH时') as insert_time,
        'yb' as source
from default_time_df 
    left outer join weibo_df on default_time_df.key_as_string = weibo_df.key_as_string
    left outer join app_df on default_time_df.key_as_string = app_df.key_as_string
    left outer join blog_df on default_time_df.key_as_string = blog_df.key_as_string
    left outer join wechat_df on default_time_df.key_as_string = wechat_df.key_as_string
    left outer join zimeiti_df on default_time_df.key_as_string = zimeiti_df.key_as_string
    left outer join foreign_news_df on default_time_df.key_as_string = foreign_news_df.key_as_string
    left outer join tianji_luntan_df on default_time_df.key_as_string = tianji_luntan_df.key_as_string
    left outer join news_df on default_time_df.key_as_string = news_df.key_as_string
    left outer join facebook_df on default_time_df.key_as_string = facebook_df.key_as_string
    left outer join tianji_luntan_comment_df on default_time_df.key_as_string = tianji_luntan_comment_df.key_as_string
    left outer join tianji_zhihu_df on default_time_df.key_as_string = tianji_zhihu_df.key_as_string
    left outer join twitter_df on default_time_df.key_as_string = twitter_df.key_as_string
    left outer join tianji_zhihu_comment_df on default_time_df.key_as_string = tianji_zhihu_comment_df.key_as_string
    left outer join yangban_news_comment_df on default_time_df.key_as_string = yangban_news_comment_df.key_as_string
where default_time_df.key_as_string >= '{last_max_time_str}'  -- 修改：使用 >= 而不是 >
order by default_time_df.key_as_string asc
    """)

    # 添加调试信息：打印过滤后的记录数
    print(f"DEBUG: 过滤后的记录数：{all_table_df.count()}")
    if all_table_df.count() > 0:
        print("DEBUG: 过滤后的数据样本：")
        all_table_df.show(3)
    else:
        print("DEBUG: 警告！过滤后没有数据，请检查时间过滤条件")

    return all_table_df

def get_search_time():
    begin_time = datetime.now() - timedelta(days=3)
    search_begin_time = begin_time.strftime("%Y-%m-%d %H:00:00")
    end_time = datetime.now() - timedelta(hours=1)
    search_end_time = end_time.strftime("%Y-%m-%d %H:59:59")
    print(f"查询时间范围：开始时间：{search_begin_time}，结束时间：{search_end_time}")
    return (search_begin_time, search_end_time)

def query_es(index=str):
    url = f"http://************:9200/{index}/_search"
    headers = {"Content-Type": "application/json"}
    auth = ("elastic", "Elastic@2025")
    search_begin_time, search_end_time = get_search_time()
    query = { "size": 0, "query": { "range": { "trs_moye_input_time": { "gte": search_begin_time, "lte": search_end_time } } }, "aggs": { "hourly_stats": { "date_histogram": { "field": "trs_moye_input_time", "calendar_interval": "1h", "format": "yyyy-MM-dd HH:00:00" } } } }
    response = requests.post(url, json=query, headers=headers, auth=auth)
    result_dict = response.json()
    buckets = result_dict["aggregations"]["hourly_stats"]["buckets"]
    print(f"索引：{index}，统计结果数量：{len(buckets)}")
    return buckets

def write_to_ck(
    df=DataFrame,
    host=str,
    port=int,
    database=str,
    table_name=str,
    username=str,
    password=str,
):
    print(f"DEBUG: 准备写入数据库，记录数：{df.count()}")
    if df.count() == 0:
        print("DEBUG: 警告！没有数据需要写入")
        return

    url = f"jdbc:clickhouse://{host}:{port}/{database}"
    prop = {
        "user": username,
        "password": password,
        "driver": "com.clickhouse.jdbc.ClickHouseDriver",
        "createTableOptions": "ENGINE = MergeTree() ORDER BY hour",
    }
    df.write.mode("append").jdbc(url, table_name, properties=prop)
    print("DEBUG: 数据写入成功")

def create_default_time_table():
    search_begin_time, search_end_time = get_search_time()
    begin_time = datetime.strptime(search_begin_time, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(search_end_time, "%Y-%m-%d %H:%M:%S")
    time_range = split_time_by_hour(begin_time, end_time)
    default_time_table = []
    for time_point in time_range:
        row = {}
        row["key_as_string"] = time_point
        default_time_table.append(row)
    default_time_df = spark.read.json(spark.sparkContext.parallelize([default_time_table]))
    default_time_df.createOrReplaceTempView("default_time_df")
    print("default_time_df表数据：")
    default_time_df.show()


def split_time_by_hour(begin_time: datetime, end_time: datetime):
    time_range = []
    current_time = begin_time
    while current_time <= end_time:
        time_range.append(current_time.strftime("%Y-%m-%d %H:00:00"))
        current_time += timedelta(hours=1)
    return time_range

def count_all_table_rows():
    #默认df，查询结果为空时使用，以防createOrReplaceTempView报错
    current_time = datetime.now() - timedelta(hours=1)
    current_time_str =current_time.strftime("%Y-%m-%d %H:00:00")
    data = {
        "key_as_string": current_time_str,
        "doc_count": 0
    }
    default_df = spark.read.json(spark.sparkContext.parallelize([data]))

    # 105 央办-微博 dwd_ybsscl_yangban_weibo
    weibo_rows = query_es("dwd_ybsscl_yangban_weibo")
    if len(weibo_rows) == 0:
        weibo_df = default_df
    else:
        weibo_df = spark.createDataFrame(weibo_rows)
    weibo_df.createOrReplaceTempView("weibo_df")
    print("微博统计结果：")
    weibo_df.show()

    # 113 央办-APP dwd_ybsscl_yangban_app
    app_rows = query_es("dwd_ybsscl_yangban_app")
    if len(app_rows) == 0:
        app_df = default_df
    else:
        app_df = spark.createDataFrame(app_rows)
    app_df.createOrReplaceTempView("app_df")
    print("APP统计结果：")
    app_df.show()

    # 121 央办-博客 dwd_ybsscl_yangban_boke
    blog_rows = query_es("dwd_ybsscl_yangban_boke")
    if len(blog_rows) == 0:
        blog_df = default_df
    else:
        blog_df = spark.createDataFrame(blog_rows)
    blog_df.createOrReplaceTempView("blog_df")
    print("博客统计结果：")
    blog_df.show()

    # 132 央办-微信 dwd_ybsscl_yangban_weixin
    wechat_rows = query_es("dwd_ybsscl_yangban_weixin")
    if len(wechat_rows) == 0:
        wechat_df = default_df
    else:
        wechat_df = spark.createDataFrame(wechat_rows)
    wechat_df.createOrReplaceTempView("wechat_df")
    print("微信统计结果：")
    wechat_df.show()

    # 135 央办-自媒体 dwd_ybsscl_zimeiti
    zimeiti_rows = query_es("dwd_ybsscl_zimeiti")
    if len(zimeiti_rows) == 0:
        zimeiti_df = default_df
    else:
        zimeiti_df = spark.createDataFrame(zimeiti_rows)
    zimeiti_df.createOrReplaceTempView("zimeiti_df")
    print("自媒体统计结果：")
    zimeiti_df.show()

    # 142 央办-境外新闻 dwd_ybsscl_yb_foreign_news_source_msg_mq
    foreign_news_rows = query_es("dwd_ybsscl_yb_foreign_news_source_msg_mq")
    if len(foreign_news_rows) == 0:
        foreign_news_df = default_df
    else:
        foreign_news_df = spark.createDataFrame(foreign_news_rows)
    foreign_news_df.createOrReplaceTempView("foreign_news_df")
    print("境外新闻统计结果：")
    foreign_news_df.show()

    # 144 央办-天玑推送-论坛 dwd_trssscl_tjts_luntan
    tianji_luntan_rows = query_es("dwd_trssscl_tjts_luntan")
    if len(tianji_luntan_rows) == 0:
        tianji_luntan_df = default_df
    else:
        tianji_luntan_df = spark.createDataFrame(tianji_luntan_rows)
    tianji_luntan_df.createOrReplaceTempView("tianji_luntan_df")
    print("天玑推送-论坛统计结果：")
    tianji_luntan_df.show()

    # 156 央办-新闻 dwd_ybsscl_yb_news
    news_rows = query_es("dwd_ybsscl_yb_news")
    if len(news_rows) == 0:
        news_df = default_df
    else:
        news_df = spark.createDataFrame(news_rows)
    news_df.createOrReplaceTempView("news_df")
    print("新闻统计结果：")
    news_df.show()

    # 158 央办-facebook dwd_ybsscl_yangban_facebook
    facebook_rows = query_es("dwd_ybsscl_yangban_facebook")
    if len(facebook_rows) == 0:
        facebook_df = default_df
    else:
        facebook_df = spark.createDataFrame(facebook_rows)
    facebook_df.createOrReplaceTempView("facebook_df")
    print("facebook统计结果：")
    facebook_df.show()

    # 159 央办-天玑推送-论坛评论 dwd_trssscl_tjts_luntanpinglun
    tianji_luntan_comment_rows = query_es("dwd_trssscl_tjts_luntanpinglun")
    if len(tianji_luntan_comment_rows) == 0:
        tianji_luntan_comment_df = default_df
    else:
        tianji_luntan_comment_df = spark.createDataFrame(tianji_luntan_comment_rows)
    tianji_luntan_comment_df.createOrReplaceTempView("tianji_luntan_comment_df")
    print("天玑推送-论坛评论统计结果：")
    tianji_luntan_comment_df.show()

    # 161 央办-天玑推送-知乎 dwd_trssscl_tjts_zhihu
    tianji_zhihu_rows = query_es("dwd_trssscl_tjts_zhihu")
    if len(tianji_zhihu_rows) == 0:
        tianji_zhihu_df = default_df
    else:
        tianji_zhihu_df = spark.createDataFrame(tianji_zhihu_rows)
    tianji_zhihu_df.createOrReplaceTempView("tianji_zhihu_df")
    print("天玑推送-知乎统计结果：")
    tianji_zhihu_df.show()

    # 165 央办-twitter dwd_ybsscl_yb_twitter_source_msg_mq
    twitter_rows = query_es("dwd_ybsscl_yb_twitter_source_msg_mq")
    if len(twitter_rows) == 0:
        twitter_df = default_df
    else:
        twitter_df = spark.createDataFrame(twitter_rows)
    twitter_df.createOrReplaceTempView("twitter_df")
    print("twitter统计结果：")
    twitter_df.show()

    # 166 央办-天玑推送-知乎评论 dwd_trssscl_tjts_zhihupinglun
    tianji_zhihu_comment_rows = query_es("dwd_trssscl_tjts_zhihupinglun")
    if len(tianji_zhihu_comment_rows) == 0:
        tianji_zhihu_comment_df = default_df
    else:
        tianji_zhihu_comment_df = spark.createDataFrame(tianji_zhihu_comment_rows)
    tianji_zhihu_comment_df.createOrReplaceTempView("tianji_zhihu_comment_df")
    print("天玑推送-知乎评论统计结果：")
    tianji_zhihu_comment_df.show()

    # 208 央办-新闻评论 dwd_ybsscl_news_comment
    yangban_news_comment_rows = query_es("dwd_ybsscl_news_comment")
    if len(yangban_news_comment_rows) == 0:
        yangban_news_comment_df = default_df
    else:
        yangban_news_comment_df = spark.createDataFrame(yangban_news_comment_rows)
    yangban_news_comment_df.createOrReplaceTempView("yangban_news_comment_df")
    print("新闻评论统计结果：")
    yangban_news_comment_df.show()

def create_recent_count_df():
    data_save_count = spark.read \
        .format("jdbc") \
        .option("url", "jdbc:clickhouse://************:8123/data_display") \
        .option("dbtable", "dws_display_save_count") \
        .option("user", "admin") \
        .option("password", "trsadmin@1234") \
        .option("driver", "com.clickhouse.jdbc.ClickHouseDriver") \
        .load()
    data_save_count.createOrReplaceTempView("data_save_count")
    print(f"data_save_count表数据如下：")
    data_save_count.show()

def get_last_max_time_str():
    read_sql = "SELECT * FROM dws_display_save_count ORDER BY hour desc limit 1"
    data_save_count = spark.read \
        .format("jdbc") \
        .option("url", "jdbc:clickhouse://************:8123/data_display") \
        .option("query", read_sql) \
        .option("user", "admin") \
        .option("password", "trsadmin@1234") \
        .option("driver", "com.clickhouse.jdbc.ClickHouseDriver") \
        .load()

    if data_save_count.isEmpty():
        last_max_time_str = "2025-04-14 23:00:00"
    else:
        last_max_time_str = data_save_count.first()["key_as_string"]
        print(f"DEBUG: 从数据库获取的原始时间字符串：{last_max_time_str}")

        # 如果原始时间是"yyyy年MM月dd日HH时"格式，需要转换
        if "年" in last_max_time_str:
            from pyspark.sql.functions import to_timestamp, date_format
            temp_df = spark.createDataFrame([(last_max_time_str,)], ["hour"])
            last_max_time_str = temp_df.select(
                date_format(to_timestamp("hour", "yyyy年MM月dd日HH时"), "yyyy-MM-dd HH:00:00")
            ).first()[0]

    print(f"DEBUG: 转换后的时间字符串：{last_max_time_str}")
    return last_max_time_str

def main():
    print("=" * 50)
    print("开始执行任务")
    print("=" * 50)

    # 生成统计时间表
    create_default_time_table()

    # 获取各个数据源的统计数据
    count_all_table_rows()

    # 生成综合统计表
    all_table_df = create_table_rows()
    print("最终表数据：")
    all_table_df.show()

    # 写入数据库
    write_to_ck(
        df=all_table_df,
        host="************",
        port=8123,
        database="data_display",
        username="admin",
        password="trsadmin@1234",
        table_name="dws_display_save_count",
    )

    print("=" * 50)
    print("任务执行完成")
    print("=" * 50)

def main_test():
    # 生成统计时间表
    get_last_max_time_str()


if __name__ == "__main__":
    main()
