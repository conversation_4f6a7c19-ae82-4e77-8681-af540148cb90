#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的西藏2.py脚本
主要测试protobuf错误的修复效果
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_connection():
    """测试数据库连接"""
    try:
        from 西藏2 import create_hybase_connection, host, port, username, password
        print("正在测试数据库连接...")
        conn = create_hybase_connection(host, port, username, password)
        print("数据库连接成功")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def test_safe_query():
    """测试安全查询函数"""
    try:
        from 西藏2 import safe_category_query_with_reconnect, create_hybase_connection, host, port, username, password

        # 测试一个简单的查询
        test_table = "microblog.tc_msg_microblog"
        test_where = "m_publish_time:[1754622000000 TO 1754628536157] AND g_asp:trsdc"

        print(f"正在测试安全查询: {test_table}")
        result = safe_category_query_with_reconnect(test_table, test_where, "trs_m_publish_hour", 100, 2)

        if result is not None:
            print(f"查询成功，结果数量: {result.getNumFound()}")
        else:
            print("查询失败，但程序没有崩溃")

    except Exception as e:
        print(f"测试安全查询时发生错误: {str(e)}")


def test_problematic_tables():
    """测试问题表管理功能"""
    try:
        from 西藏2 import add_problematic_table, is_problematic_table, PROBLEMATIC_TABLES

        print("测试问题表管理功能...")

        # 添加一个测试表
        test_table = "test.problematic_table"
        add_problematic_table(test_table)

        # 检查是否被正确添加
        if is_problematic_table(test_table):
            print(f"✓ 表 {test_table} 已正确添加到问题表列表")
        else:
            print(f"✗ 表 {test_table} 添加失败")

        print(f"当前问题表列表: {PROBLEMATIC_TABLES}")

    except Exception as e:
        print(f"测试问题表管理时发生错误: {str(e)}")


def test_query_condition_cleaning():
    """测试查询条件清理功能"""
    try:
        from 西藏2 import clean_query_condition

        print("测试查询条件清理功能...")

        test_conditions = [
            "m_publish_time:[1754622000000 TO 1754628536157] AND g_asp:trsdc",
            "  m_publish_time:[1754622000000   TO   1754628536157]   AND   g_asp:trsdc  ",
            "",
            None
        ]

        for condition in test_conditions:
            try:
                cleaned = clean_query_condition(condition)
                print(f"原始: '{condition}' -> 清理后: '{cleaned}'")
            except Exception as e:
                print(f"清理条件 '{condition}' 时出错: {str(e)}")

    except Exception as e:
        print(f"测试查询条件清理时发生错误: {str(e)}")

def test_empty_rows():
    """测试空行创建函数"""
    try:
        from 西藏2 import create_empty_rows
        
        # 创建测试时间范围
        time_range = ["2025080812", "2025080813", "2025080814"]
        
        print("正在测试空行创建...")
        empty_rows = create_empty_rows(time_range)
        
        print(f"创建了 {len(empty_rows)} 个空行")
        if empty_rows:
            print("第一行示例:")
            for key, value in empty_rows[0].items():
                print(f"  {key}: {value}")
                
    except Exception as e:
        print(f"测试空行创建时发生错误: {str(e)}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("开始测试修复后的西藏2.py脚本")
    print("=" * 60)

    # 测试1: 数据库连接
    print("\n1. 测试数据库连接")
    print("-" * 30)
    conn = test_connection()

    # 测试2: 安全查询
    print("\n2. 测试安全查询函数")
    print("-" * 30)
    test_safe_query()

    # 测试3: 问题表管理
    print("\n3. 测试问题表管理功能")
    print("-" * 30)
    test_problematic_tables()

    # 测试4: 查询条件清理
    print("\n4. 测试查询条件清理功能")
    print("-" * 30)
    test_query_condition_cleaning()

    # 测试5: 空行创建
    print("\n5. 测试空行创建函数")
    print("-" * 30)
    test_empty_rows()

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
