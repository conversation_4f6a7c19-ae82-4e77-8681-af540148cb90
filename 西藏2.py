from hybaseapi.TRSConnection import TRSConnection
from hybaseapi.ConnectParams import ConnectParams
from hybaseapi.SearchParams import SearchParams
from datetime import datetime, timedelta
from pyspark.sql import SparkSession, DataFrame
import time
import traceback

# 配置 Hybase 数据库连接
host = "***********"
port = "5555"
username = "admin"
password = "trs@300229"
tables_and_conditions = {
    "microblog.tc_msg_microblog": "weibo",  # 微博
    "wechat.tc_msg_wechat_public_group_20201225": "wechat",  # 微信
    "other.tc_msg_news": "news",  # 境内新闻网站
    "other.tc_msg_news_app_trs": "app",  # app
    "other.tc_msg_news_comment": "news_comment",  # 新闻评论
    "other.tc_msg_blog": "blog",  # 博客
    "other.tc_msg_selfmedia": "self_media",  # 自媒体
    "other.tc_msg_abroad_news_article": "abroad_news",  # 境外新闻
    "other.tc_msg_abroad_forum": "abroad_forum",  # 境外论坛贴吧
}
mutiple_table_platform_list = [
    {
        "label": "论坛贴吧",
        "platform": "forum_tieba",
        "tableNames": ["other.tc_msg_tieba", "other.tc_msg_forum"],
    },
]
table_inner_platform_list = [
    {
        "label": "脸书",
        "platform": "facebook",
        "tableName": "other.tc_msg_social",
        "conditions": ["trs_m_site_name:Facebook"],
    },
    {
        "label": "推特",
        "platform": "twitter",
        "tableName": "other.tc_msg_social",
        "conditions": ["trs_m_site_name:Twitter"],
    },
]

publish_time_simple_table_platform_map = {
    "other.tc_msg_selfmedia_comment": "self_media_comment",  # 自媒体评论
    "other.tc_msg_short_video_comment": "short_video_comment",  # 短视频评论
}

publish_time_mutiple_table_platform_list = [
    {
        "label": "其他",
        "platform": "other",
        "tableNames": [
            "wechat.tc_msg_wechat_pyq",
            "other.tc_msg_app_sw",
            "other.tc_msg_qq_dt",
        ],
    },
]

# 返回空的 DataFrame
spark = SparkSession.builder.appName("Hybase Data Processing").getOrCreate()


def create_hybase_connection(host: str, port: int, username: str, password: str):
    url = f"http://{host.split(',')[0]}:{port}"
    return TRSConnection(url, username, password, ConnectParams())

def safe_category_query(conn: TRSConnection, table_name: str, where: str, group_field: str, max_results: int = 1000000, max_retries: int = 3):

    for attempt in range(max_retries):
        try:
            print(f"正在查询表 {table_name}，尝试第 {attempt + 1} 次...")
            resultSet = conn.categoryQuery(table_name, where, None, group_field, max_results)
            return resultSet
        except Exception as e:
            print(f"查询表 {table_name} 失败，尝试第 {attempt + 1} 次，错误: {str(e)}")
            if attempt < max_retries - 1:
                print(f"等待 {(attempt + 1) * 2} 秒后重试...")
                time.sleep((attempt + 1) * 2)  # 递增等待时间
            else:
                print(f"查询表 {table_name} 最终失败，已达到最大重试次数")
                traceback.print_exc()
                return None
    return None


def write_to_ck(
    df=DataFrame,
    host=str,
    port=int,
    database=str,
    table_name=str,
    username=str,
    password=str,
):
    url = f"jdbc:clickhouse://{host}:{port}/{database}"
    prop = {
        "user": username,
        "password": password,
        "driver": "com.clickhouse.jdbc.ClickHouseDriver",
        "createTableOptions": "ENGINE = MergeTree() ORDER BY hour",
    }
    df.write.mode("append").jdbc(url, table_name, properties=prop)

def convert_insert_time_category_map(category_map: dict):
    """
    将m_insert_hybase_time格式的categoryMap转换为小时级格式

    参数:
        category_map: 原始分类统计结果，key格式如"2025/08/08 06:40:14"

    返回:
        dict: 转换后的分类统计结果，key格式如"2025080806"
    """
    new_category_map = {}
    for time_str, count in category_map.items():
        try:
            # 解析时间字符串：2025/08/08 06:40:14
            if isinstance(time_str, str) and '/' in time_str and ' ' in time_str:
                date_part, time_part = time_str.split(' ')
                year, month, day = date_part.split('/')
                hour = time_part.split(':')[0]
                # 转换为小时级格式：2025080806
                hour_key = f"{year}{month.zfill(2)}{day.zfill(2)}{hour.zfill(2)}"
                new_category_map[hour_key] = new_category_map.get(hour_key, 0) + count
            else:
                print(f"警告：无法解析时间格式: {time_str}")
        except Exception as e:
            print(f"时间转换错误: {time_str}, 错误: {str(e)}")
    return new_category_map


def split_time_by_hour(begin_time: datetime, end_time: datetime):
    time_range = []
    current_time = begin_time
    while current_time <= end_time:
        time_range.append(current_time.strftime("%Y%m%d%H"))
        current_time += timedelta(hours=1)
    return time_range


def get_query_params():
    #start_date = "#{system.last_success_execution_datetime}"
    start_date = "2025-08-08 00:00:00"
    begin_time = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.now()

    time_range = split_time_by_hour(begin_time, end_time)
    return (
        time_range,
        f"m_insert_hybase_time:[{int(begin_time.timestamp()*1000)} TO {int(end_time.timestamp()*1000)}]",
    )



def query_for_category_maps(conn: TRSConnection, where: str):
    """
    查询基础表的分类统计数据
    修复：正确处理m_insert_hybase_time的时间格式转换

    参数:
        conn: 数据库连接
        where: 查询条件

    返回:
        dict: 各平台的分类统计结果
    """
    category_maps = {}
    for table_name, platform in tables_and_conditions.items():
        # 动态替换微信表字段
        if table_name == "wechat.tc_msg_wechat_public_group_20201225":
            modified_where = where.replace("g_asp:", "trs_g_asp:")
            replace_log = f"（已替换g_asp->trs_g_asp）"
        else:
            modified_where = where
            replace_log = ""

        # 执行安全查询
        resultSet = safe_category_query(conn, table_name, modified_where, "m_insert_hybase_time")

        if resultSet is not None:
            # 打印带条件的调试信息
            print(
                f"平台：{platform}，表名：{table_name}\n"
                f"原始条件: {where}\n"
                f"实际条件: {modified_where}{replace_log}\n"
                f"查询结果: {resultSet.getNumFound()}条"
                "\n————————————————————"
            )
            # 关键修复：转换时间格式
            raw_category_map = resultSet.getCategoryMap()
            converted_category_map = convert_insert_time_category_map(raw_category_map)
            category_maps[platform] = converted_category_map

            # 调试信息
            print(f"原始分类键样例: {list(raw_category_map.keys())[:3] if raw_category_map else '无数据'}")
            print(f"转换后分类键样例: {list(converted_category_map.keys())[:3] if converted_category_map else '无数据'}")
        else:
            print(f"平台：{platform}，表名：{table_name} 查询失败，使用空结果")
            category_maps[platform] = {}
    return category_maps



def query_mutiple_table_platform_for_category_maps(
    conn: TRSConnection, where: str, time_range: list
):
    """
    查询多表平台的分类统计数据
    修复：正确处理m_insert_hybase_time的时间格式转换

    参数:
        conn: 数据库连接
        where: 查询条件
        time_range: 时间范围列表

    返回:
        dict: 各平台的分类统计结果
    """
    category_maps = {}
    for platform in mutiple_table_platform_list:
        category_map_list = []
        platformName = platform["platform"]
        for table_name in platform["tableNames"]:
            resultSet = safe_category_query(conn, table_name, where, "m_insert_hybase_time")
            if resultSet is not None:
                print(
                    f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
                )
                # 关键修复：转换时间格式
                raw_category_map = resultSet.getCategoryMap()
                converted_category_map = convert_insert_time_category_map(raw_category_map)
                category_map_list.append(converted_category_map)
            else:
                print(f"平台：{platformName}，表名：{table_name} 查询失败，使用空结果")
                category_map_list.append({})
        category_maps[platformName] = merge_category_map_list(
            time_range, category_map_list
        )
    return category_maps



def merge_category_map_list(time_range: list, category_map_list: list):
    new_category_map = {}
    for time_point in time_range:
        count = 0
        for category_map in category_map_list:
            count += category_map.get(time_point, 0)
        new_category_map[time_point] = count
    return new_category_map


def query_table_inner_platform_for_category_maps(conn: TRSConnection, where: str):
    """
    查询表内平台的分类统计数据
    修复：正确处理m_insert_hybase_time的时间格式转换

    参数:
        conn: 数据库连接
        where: 查询条件

    返回:
        dict: 各平台的分类统计结果
    """
    category_maps = {}
    for platform in table_inner_platform_list:
        platformName = platform["platform"]
        table_name = platform["tableName"]
        platform_conditions = platform["conditions"]
        conditions = where
        for pc in platform_conditions:
            conditions += f" AND {pc}"

        resultSet = safe_category_query(conn, table_name, conditions, "m_insert_hybase_time")
        if resultSet is not None:
            print(
                f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
            )
            # 关键修复：转换时间格式
            raw_category_map = resultSet.getCategoryMap()
            converted_category_map = convert_insert_time_category_map(raw_category_map)
            category_maps[platformName] = converted_category_map
        else:
            print(f"平台：{platformName}，表名：{table_name} 查询失败，使用空结果")
            category_maps[platformName] = {}
    return category_maps


def query_publish_time_simple_table_platform_for_category_maps(
    conn: TRSConnection, where: str
):
    """
    查询简单发布时间表平台的分类统计数据
    修复：统一使用m_insert_hybase_time并进行格式转换

    参数:
        conn: 数据库连接
        where: 查询条件

    返回:
        dict: 各平台的分类统计结果
    """
    category_maps = {}
    for table_name, platform in publish_time_simple_table_platform_map.items():
        group_fields = "m_insert_hybase_time"
        resultSet = safe_category_query(conn, table_name, where, group_fields)
        if resultSet is not None:
            print(
                f"平台：{platform}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
            )
            # 关键修复：使用新的时间转换函数
            raw_category_map = resultSet.getCategoryMap()
            converted_category_map = convert_insert_time_category_map(raw_category_map)
            category_maps[platform] = converted_category_map
        else:
            print(f"平台：{platform}，表名：{table_name} 查询失败，使用空结果")
            category_maps[platform] = {}
    return category_maps



def convert_category_map(category_map: dict):
    new_category_map = {}
    for k, v in category_map.items():
        new_category_map[f"{k[:4]}{k[5:7]}{k[8:10]}00"] = v
    return new_category_map


def query_publish_time_mutiple_table_platform_for_category_maps(
    conn: TRSConnection, where: str, time_range: list
):

    category_maps = {}
    for platform in publish_time_mutiple_table_platform_list:
        category_map_list = []
        platformName = platform["platform"]
        for table_name in platform["tableNames"]:
            resultSet = safe_category_query(conn, table_name, where, "m_insert_hybase_time")
            if resultSet is not None:
                categoryMap = convert_category_map(resultSet.getCategoryMap())
                print(
                    f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
                )
                category_map_list.append(categoryMap)
            else:
                print(f"平台：{platformName}，表名：{table_name} 查询失败，使用空结果")
                category_map_list.append({})
        category_maps[platformName] = merge_category_map_list(
            time_range, category_map_list
        )
    return category_maps


def create_table_rows(category_maps: dict, time_range: list):
    """
    创建表记录，增加详细的调试信息

    参数:
        category_maps: 各平台的分类统计结果
        time_range: 时间范围列表

    返回:
        list: 统计数据行列表
    """
    rows = []
    print(f"开始创建表记录，时间范围: {len(time_range)} 个时间点")
    print(f"时间范围样例: {time_range[:3] if time_range else '无数据'}")

    # 打印每个平台的分类统计情况
    for platform, category_map in category_maps.items():
        if category_map:
            print(f"平台 {platform}: {len(category_map)} 个时间点，样例键: {list(category_map.keys())[:3]}")
        else:
            print(f"平台 {platform}: 无数据")

    for time_point in time_range:
        row = {}
        total = 0
        dt = datetime.strptime(time_point, "%Y%m%d%H")
        formatted_time = dt.strftime("%Y年%m月%d日%H时")

        # 从格式化的字符串提取所需的部分
        row["year"] = formatted_time[:5]  # 'xxxx年'
        row["month"] = formatted_time[:8]  # 'xx月'
        row["day"] = formatted_time[:11]  # 'xx日'
        row["hour"] = formatted_time  # 'xxxx年xx月xx日xx小时'
        row["insert_time"] = dt

        # 统计每个时间点的数据
        time_point_total = 0
        for platform, category_map in category_maps.items():
            count = category_map.get(time_point, 0)
            row[f"{platform}_count"] = count
            total += count
            time_point_total += count
            if count > 0:
                print(f"时间点 {time_point}, 平台 {platform}: {count} 条记录")

        row["total_count"] = total
        if time_point_total > 0:
            print(f"时间点 {time_point} 总计: {time_point_total} 条记录")
        rows.append(row)

    total_records = sum(row['total_count'] for row in rows)
    print(f"最终生成 {len(rows)} 行记录，总数据量: {total_records}")
    return rows


def merge_platform_category_map_result(platform_category_map_list: list):
    new_category_map = {}
    for platform_category_map in platform_category_map_list:
        for k, v in platform_category_map.items():
            new_category_map[k] = v
    return new_category_map


def build_disanfang_rows(conn: TRSConnection, where: str, time_range: list):
    """
    构建第三方数据统计行

    参数:
        conn: 数据库连接
        where: 查询条件
        time_range: 时间范围列表

    返回:
        list: 统计数据行列表
    """
    print(
        "==========================================开始第三方数据统计======================================="
    )
    new_where = where + " AND g_asp:trsdc "
    print(f"开始查询数据库，基础查询where：{new_where}")

    try:
        category_maps0 = query_for_category_maps(conn, new_where)
        category_maps1 = query_mutiple_table_platform_for_category_maps(
            conn, new_where, time_range
        )
        category_maps2 = query_table_inner_platform_for_category_maps(conn, new_where)
        category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
            conn, new_where
        )
        category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
            conn, new_where, time_range
        )
        category_maps = merge_platform_category_map_result(
            [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
        )
        print("查询数据库结束:")
        # 封装数据库表记录
        rows = create_table_rows(category_maps, time_range)
        for row in rows:
            row["g_asp"] = "trsdc"
        print(
            "==========================================第三方数据统计结束======================================="
        )
        return rows
    except Exception as e:
        print(f"第三方数据统计过程中发生错误: {str(e)}")
        traceback.print_exc()
        return []
def build_yangban_rows(conn: TRSConnection, where: str, time_range: list):
    """
    构建央办数据统计行

    参数:
        conn: 数据库连接
        where: 查询条件
        time_range: 时间范围列表

    返回:
        list: 统计数据行列表
    """
    print(
        "==========================================开始央办数据统计======================================="
    )
    new_where = where + " AND g_asp:yb "
    print(f"开始查询数据库，基础查询where：{new_where}")

    try:
        category_maps0 = query_for_category_maps(conn, new_where)
        category_maps1 = query_mutiple_table_platform_for_category_maps(
            conn, new_where, time_range
        )
        category_maps2 = query_table_inner_platform_for_category_maps(conn, new_where)
        category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
            conn, new_where
        )
        category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
            conn, new_where, time_range
        )
        category_maps = merge_platform_category_map_result(
            [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
        )
        print("查询数据库结束:")
        # 封装数据库表记录
        rows = create_table_rows(category_maps, time_range)
        for row in rows:
            row["g_asp"] = "yb"
        print(
            "==========================================央办数据统计结束======================================="
        )
        return rows
    except Exception as e:
        print(f"央办数据统计过程中发生错误: {str(e)}")
        traceback.print_exc()
        return []

def build_bendicaiji_rows(conn: TRSConnection, where: str, time_range: list):
    """
    构建本地采集数据统计行

    参数:
        conn: 数据库连接
        where: 查询条件
        time_range: 时间范围列表

    返回:
        list: 统计数据行列表
    """
    print(
        "==========================================开始本地采集数据统计======================================="
    )
    new_where = where + " AND g_asp:trslc "
    print(f"开始查询数据库，基础查询where：{new_where}")

    try:
        category_maps0 = query_for_category_maps(conn, new_where)
        category_maps1 = query_mutiple_table_platform_for_category_maps(
            conn, new_where, time_range
        )
        category_maps2 = query_table_inner_platform_for_category_maps(conn, new_where)
        category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
            conn, new_where
        )
        category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
            conn, new_where, time_range
        )
        category_maps = merge_platform_category_map_result(
            [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
        )
        print("查询数据库结束:")
        # 封装数据库表记录
        rows = create_table_rows(category_maps, time_range)
        for row in rows:
            row["g_asp"] = "trslc"
        print(
            "==========================================本地采集统计结束======================================="
        )
        return rows
    except Exception as e:
        print(f"本地采集数据统计过程中发生错误: {str(e)}")
        traceback.print_exc()
        return []

def main250424():
    # 拼装查询条件
    time_range, where = get_query_params()
    print(where)
    # 创建海贝数据库连接
    conn = create_hybase_connection(host, port, username, password)
    # 查询数据库
    category_maps0 = query_for_category_maps(conn, where)
    category_maps1 = query_mutiple_table_platform_for_category_maps(
        conn, where, time_range
    )
    category_maps2 = query_table_inner_platform_for_category_maps(conn, where)
    category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
        conn, where
    )
    category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
        conn, where, time_range
    )
    category_maps = merge_platform_category_map_result(
        [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
    )
    print("分类统计结果:" + str(category_maps))
    # 封装数据库表记录
    rows = create_table_rows(category_maps, time_range)
    print("表记录：")
    for row in rows:
        print(row)
    df = spark.createDataFrame(rows)
    # 写入数据库
    write_to_ck(
        df=df,
        host="************",
        port=8123,
        database="data_display",
        username="admin",
        password="trsadmin@1234",
        table_name="dwd_display_distribution",
    )


def main_test():
    # 拼装查询条件
    time_range, where = get_query_params()
    print(f"查询时间范围：{where}")
    conn = create_hybase_connection(host, port, username, password)
    disanfang_rows = build_disanfang_rows(conn, where, time_range)
    yangban_rows = build_yangban_rows(conn, where, time_range)
    df = spark.createDataFrame(disanfang_rows + yangban_rows)
    print("表记录：")
    df.show(1)


def main():
    """
    主函数，执行完整的数据统计和写入流程
    """
    try:
        # 拼装查询条件
        time_range, where = get_query_params()
        print(f"查询时间范围：{where}")

        # 创建数据库连接
        conn = create_hybase_connection(host, port, username, password)

        # 分别构建各类数据统计
        disanfang_rows = build_disanfang_rows(conn, where, time_range)
        yangban_rows = build_yangban_rows(conn, where, time_range)
        bengdicaiji_rows = build_bendicaiji_rows(conn, where, time_range)

        # 合并所有数据
        all_rows = disanfang_rows + yangban_rows + bengdicaiji_rows
        df = spark.createDataFrame(all_rows)
        print("表记录：")
        df.show(1)

        # 写入ClickHouse
        write_to_ck(
            df=df,
            host="************",
            port=8123,
            database="data_display",
            username="admin",
            password="trsadmin@1234",
            table_name="dwd_display_distribution",
        )
        print("数据写入ClickHouse成功")

    except Exception as e:
        print(f"主函数执行过程中发生错误: {str(e)}")
        traceback.print_exc()
        raise e  # 重新抛出异常以便Spark能够正确处理


if __name__ == "__main__":
    main()
