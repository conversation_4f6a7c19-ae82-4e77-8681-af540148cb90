10:31:33  25/08/15 10:31:33 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
10:31:34  25/08/15 10:31:34 INFO SparkContext: Running Spark version 3.5.4
10:31:34  25/08/15 10:31:34 INFO SparkContext: OS info Linux, 4.18.0-193.28.1.el7.aarch64, aarch64
10:31:34  25/08/15 10:31:34 INFO SparkContext: Java version 17.0.14
10:31:34  25/08/15 10:31:34 INFO ResourceUtils: ==============================================================
10:31:34  25/08/15 10:31:34 INFO ResourceUtils: No custom resources configured for spark.driver.
10:31:34  25/08/15 10:31:34 INFO ResourceUtils: ==============================================================
10:31:34  25/08/15 10:31:34 INFO SparkContext: Submitted application: spark-app-process-count-local-only
10:31:34  25/08/15 10:31:34 INFO ResourceProfile: Default ResourceProfile created, executor resources: Map(cores -> name: cores, amount: 2, script: , vendor: , memory -> name: memory, amount: 1024, script: , vendor: , offHeap -> name: offHeap, amount: 0, script: , vendor: ), task resources: Map(cpus -> name: cpus, amount: 1.0)
10:31:34  25/08/15 10:31:34 INFO ResourceProfile: Limiting resource is cpus at 2 tasks per executor
10:31:34  25/08/15 10:31:34 INFO ResourceProfileManager: Added ResourceProfile id: 0
10:31:34  25/08/15 10:31:34 INFO SecurityManager: Changing view acls to: root
10:31:34  25/08/15 10:31:34 INFO SecurityManager: Changing modify acls to: root
10:31:34  25/08/15 10:31:34 INFO SecurityManager: Changing view acls groups to:
10:31:34  25/08/15 10:31:34 INFO SecurityManager: Changing modify acls groups to:
10:31:34  25/08/15 10:31:34 INFO SecurityManager: SecurityManager: authentication disabled; ui acls disabled; users with view permissions: root; groups with view permissions: EMPTY; users with modify permissions: root; groups with modify permissions: EMPTY
10:31:34  25/08/15 10:31:34 INFO Utils: Successfully started service 'sparkDriver' on port 40000.
10:31:34  25/08/15 10:31:34 INFO SparkEnv: Registering MapOutputTracker
10:31:34  25/08/15 10:31:34 INFO SparkEnv: Registering BlockManagerMaster
10:31:34  25/08/15 10:31:34 INFO BlockManagerMasterEndpoint: Using org.apache.spark.storage.DefaultTopologyMapper for getting topology information
10:31:34  25/08/15 10:31:34 INFO BlockManagerMasterEndpoint: BlockManagerMasterEndpoint up
10:31:34  25/08/15 10:31:34 INFO SparkEnv: Registering BlockManagerMasterHeartbeat
10:31:34  25/08/15 10:31:34 INFO DiskBlockManager: Created local directory at /tmp/blockmgr-b48da0a5-8490-41b7-b82c-e3c6c5262d59
10:31:34  25/08/15 10:31:34 INFO MemoryStore: MemoryStore started with capacity 434.4 MiB
10:31:34  25/08/15 10:31:34 INFO SparkEnv: Registering OutputCommitCoordinator
10:31:34  25/08/15 10:31:34 INFO JettyUtils: Start Jetty 0.0.0.0:4040 for SparkUI
10:31:34  25/08/15 10:31:34 INFO Utils: Successfully started service 'SparkUI' on port 4040.
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/commons-pool2-2.11.1.jar at spark://************:40000/jars/commons-pool2-2.11.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/lang-mustache-client-7.12.1.jar at spark://************:40000/jars/lang-mustache-client-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/jedis-3.8.0.jar at spark://************:40000/jars/jedis-3.8.0.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/hppc-0.8.1.jar at spark://************:40000/jars/hppc-0.8.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/rank-eval-client-7.12.1.jar at spark://************:40000/jars/rank-eval-client-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpcore5-h2-5.2.4.jar at spark://************:40000/jars/httpcore5-h2-5.2.4.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/commons-httpclient-3.1.jar at spark://************:40000/jars/commons-httpclient-3.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/postgresql-42.7.4.jar at spark://************:40000/jars/postgresql-42.7.4.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/lucene-core-8.8.0.jar at spark://************:40000/jars/lucene-core-8.8.0.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/SparkOnEs-1.0.jar at spark://************:40000/jars/SparkOnEs-1.0.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/spark-redis_2.12-3.1.0.jar at spark://************:40000/jars/spark-redis_2.12-3.1.0.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/fastjson-1.2.83.jar at spark://************:40000/jars/fastjson-1.2.83.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-rest-high-level-client-7.12.1.jar at spark://************:40000/jars/elasticsearch-rest-high-level-client-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpcore-nio-4.4.16.jar at spark://************:40000/jars/httpcore-nio-4.4.16.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/clickhouse-jdbc-0.6.4.jar at spark://************:40000/jars/clickhouse-jdbc-0.6.4.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-x-content-7.12.1.jar at spark://************:40000/jars/elasticsearch-x-content-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-7.12.1.jar at spark://************:40000/jars/elasticsearch-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/hybase-v10.0.5.jar at spark://************:40000/jars/hybase-v10.0.5.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpclient5-5.3.1.jar at spark://************:40000/jars/httpclient5-5.3.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/protobuf-java-3.21.12.jar at spark://************:40000/jars/protobuf-java-3.21.12.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpasyncclient-4.1.5.jar at spark://************:40000/jars/httpasyncclient-4.1.5.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/hive-jdbc-3.1.0-h0.cbu.mrs.321.r13.jar at spark://************:40000/jars/hive-jdbc-3.1.0-h0.cbu.mrs.321.r13.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-core-7.12.1.jar at spark://************:40000/jars/elasticsearch-core-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/httpcore5-5.2.4.jar at spark://************:40000/jars/httpcore5-5.2.4.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-spark-30_2.12-7.12.0.jar at spark://************:40000/jars/elasticsearch-spark-30_2.12-7.12.0.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/mysql-connector-j-8.1.0.jar at spark://************:40000/jars/mysql-connector-j-8.1.0.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added JAR file:///home/<USER>/jars/elasticsearch-rest-client-7.12.1.jar at spark://************:40000/jars/elasticsearch-rest-client-7.12.1.jar with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO SparkContext: Added file file:///home/<USER>/py-files/hybaseapi_.zip at spark://************:40000/files/hybaseapi_.zip with timestamp 1755225094126
10:31:34  25/08/15 10:31:34 INFO Utils: Copying /home/<USER>/py-files/hybaseapi_.zip to /tmp/spark-078bda7e-4bc3-4cb8-a195-fe821ec832ca/userFiles-e0a8a109-cd4c-4e59-ae69-31d4ada9ff3c/hybaseapi_.zip
10:31:34  25/08/15 10:31:34 INFO StandaloneAppClient$ClientEndpoint: Connecting to master spark://spark-manager:7077...
10:31:35  25/08/15 10:31:35 INFO TransportClientFactory: Successfully created connection to spark-manager/************:7077 after 27 ms (0 ms spent in bootstraps)
10:31:35  25/08/15 10:31:35 INFO StandaloneSchedulerBackend: Connected to Spark cluster with app ID app-20250815023135-1076
10:31:35  25/08/15 10:31:35 INFO StandaloneAppClient$ClientEndpoint: Executor added: app-20250815023135-1076/0 on worker-20250811151921-**********-39619 (**********:39619) with 2 core(s)
10:31:35  25/08/15 10:31:35 INFO StandaloneSchedulerBackend: Granted executor ID app-20250815023135-1076/0 on hostPort **********:39619 with 2 core(s), 1024.0 MiB RAM
10:31:35  25/08/15 10:31:35 INFO StandaloneAppClient$ClientEndpoint: Executor added: app-20250815023135-1076/1 on worker-20250811151924-**********-43349 (**********:43349) with 2 core(s)
10:31:35  25/08/15 10:31:35 INFO StandaloneSchedulerBackend: Granted executor ID app-20250815023135-1076/1 on hostPort **********:43349 with 2 core(s), 1024.0 MiB RAM
10:31:35  25/08/15 10:31:35 INFO Utils: Successfully started service 'org.apache.spark.network.netty.NettyBlockTransferService' on port 40200.
10:31:35  25/08/15 10:31:35 INFO NettyBlockTransferService: Server created on ************ 0.0.0.0:40200
10:31:35  25/08/15 10:31:35 INFO BlockManager: Using org.apache.spark.storage.RandomBlockReplicationPolicy for block replication policy
10:31:35  25/08/15 10:31:35 INFO BlockManagerMaster: Registering BlockManager BlockManagerId(driver, ************, 40200, None)
10:31:35  25/08/15 10:31:35 INFO StandaloneAppClient$ClientEndpoint: Executor updated: app-20250815023135-1076/1 is now RUNNING
10:31:35  25/08/15 10:31:35 INFO StandaloneAppClient$ClientEndpoint: Executor updated: app-20250815023135-1076/0 is now RUNNING
10:31:35  25/08/15 10:31:35 INFO BlockManagerMasterEndpoint: Registering block manager ************:40200 with 434.4 MiB RAM, BlockManagerId(driver, ************, 40200, None)
10:31:35  25/08/15 10:31:35 INFO BlockManagerMaster: Registered BlockManager BlockManagerId(driver, ************, 40200, None)
10:31:35  25/08/15 10:31:35 INFO BlockManager: Initialized BlockManager: BlockManagerId(driver, ************, 40200, None)
10:31:35  25/08/15 10:31:35 INFO StandaloneSchedulerBackend: SchedulerBackend is ready for scheduling beginning after reached minRegisteredResourcesRatio: 0.0
10:31:35  25/08/15 10:31:35 INFO SharedState: Setting hive.metastore.warehouse.dir ('null') to the value of spark.sql.warehouse.dir.
10:31:35  25/08/15 10:31:35 INFO SharedState: Warehouse path is 'file:/opt/spark-warehouse'.
10:31:36  25/08/15 10:31:36 INFO Version: Elasticsearch Hadoop v7.12.0 [79dace1877]
10:31:36  25/08/15 10:31:36 INFO ApacheHttpConnectionImpl: Connection TTL: 0 ms
10:31:36  25/08/15 10:31:36 INFO ApacheHttpConnectionImpl: Connection reuse strategy: LIFO
10:31:37  25/08/15 10:31:37 INFO ApacheHttpConnectionImpl: Connection TTL: 0 ms
10:31:37  25/08/15 10:31:37 INFO ApacheHttpConnectionImpl: Connection reuse strategy: LIFO
10:31:38  25/08/15 10:31:38 INFO StandaloneSchedulerBackend$StandaloneDriverEndpoint: Registered executor NettyRpcEndpointRef(spark-client://Executor) (2.49.0.0:31691) with ID 1,  ResourceProfileId 0
10:31:38  25/08/15 10:31:38 INFO BlockManagerMasterEndpoint: Registering block manager **********:40200 with 434.4 MiB RAM, BlockManagerId(1, **********, 40200, None)
10:31:38  25/08/15 10:31:38 INFO StandaloneSchedulerBackend$StandaloneDriverEndpoint: Registered executor NettyRpcEndpointRef(spark-client://Executor) (2.49.0.0:31501) with ID 0,  ResourceProfileId 0
10:31:38  25/08/15 10:31:38 INFO BlockManagerMasterEndpoint: Registering block manager **********:40200 with 434.4 MiB RAM, BlockManagerId(0, **********, 40200, None)
10:31:39  25/08/15 10:31:39 INFO CodeGenerator: Code generated in 178.823357 ms
10:31:39  25/08/15 10:31:39 INFO SparkContext: Starting job: json at NativeMethodAccessorImpl.java:0
10:31:39  25/08/15 10:31:39 INFO DAGScheduler: Got job 0 (json at NativeMethodAccessorImpl.java:0) with 2 output partitions
10:31:39  25/08/15 10:31:39 INFO DAGScheduler: Final stage: ResultStage 0 (json at NativeMethodAccessorImpl.java:0)
10:31:39  25/08/15 10:31:39 INFO DAGScheduler: Parents of final stage: List()
10:31:39  25/08/15 10:31:39 INFO DAGScheduler: Missing parents: List()
10:31:39  25/08/15 10:31:39 INFO DAGScheduler: Submitting ResultStage 0 (MapPartitionsRDD[6] at json at NativeMethodAccessorImpl.java:0), which has no missing parents
10:31:39  25/08/15 10:31:39 INFO MemoryStore: Block broadcast_0 stored as values in memory (estimated size 27.0 KiB, free 434.4 MiB)
10:31:39  25/08/15 10:31:39 INFO MemoryStore: Block broadcast_0_piece0 stored as bytes in memory (estimated size 11.1 KiB, free 434.4 MiB)
10:31:39  25/08/15 10:31:39 INFO BlockManagerInfo: Added broadcast_0_piece0 in memory on ************:40200 (size: 11.1 KiB, free: 434.4 MiB)
10:31:39  25/08/15 10:31:39 INFO SparkContext: Created broadcast 0 from broadcast at DAGScheduler.scala:1585
10:31:39  25/08/15 10:31:39 INFO DAGScheduler: Submitting 2 missing tasks from ResultStage 0 (MapPartitionsRDD[6] at json at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0, 1))
10:31:39  25/08/15 10:31:39 INFO TaskSchedulerImpl: Adding task set 0.0 with 2 tasks resource profile 0
10:31:39  25/08/15 10:31:39 INFO TaskSetManager: Starting task 0.0 in stage 0.0 (TID 0) (**********, executor 0, partition 0, PROCESS_LOCAL, 11244 bytes)
10:31:39  25/08/15 10:31:39 INFO TaskSetManager: Starting task 1.0 in stage 0.0 (TID 1) (**********, executor 1, partition 1, PROCESS_LOCAL, 13231 bytes)
10:31:40  25/08/15 10:31:40 INFO BlockManagerInfo: Added broadcast_0_piece0 in memory on **********:40200 (size: 11.1 KiB, free: 434.4 MiB)
10:31:40  25/08/15 10:31:40 INFO BlockManagerInfo: Added broadcast_0_piece0 in memory on **********:40200 (size: 11.1 KiB, free: 434.4 MiB)
10:31:41  25/08/15 10:31:41 INFO TaskSetManager: Finished task 0.0 in stage 0.0 (TID 0) in 1353 ms on ********** (executor 0) (1/2)
10:31:41  25/08/15 10:31:41 INFO TaskSetManager: Finished task 1.0 in stage 0.0 (TID 1) in 1344 ms on ********** (executor 1) (2/2)
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Removed TaskSet 0.0, whose tasks have all completed, from pool
10:31:41  25/08/15 10:31:41 INFO PythonAccumulatorV2: Connected to AccumulatorServer at host: 127.0.0.1 port: 38741
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: ResultStage 0 (json at NativeMethodAccessorImpl.java:0) finished in 1.507 s
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Job 0 is finished. Cancelling potential speculative or zombie tasks for this job
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Killing all running tasks in stage 0: Stage finished
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Job 0 finished: json at NativeMethodAccessorImpl.java:0, took 1.548488 s
10:31:41  25/08/15 10:31:41 INFO CodeGenerator: Code generated in 10.684819 ms
10:31:41  25/08/15 10:31:41 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Got job 1 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Final stage: ResultStage 1 (showString at NativeMethodAccessorImpl.java:0)
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Parents of final stage: List()
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Missing parents: List()
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Submitting ResultStage 1 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
10:31:41  25/08/15 10:31:41 INFO MemoryStore: Block broadcast_1 stored as values in memory (estimated size 25.4 KiB, free 434.3 MiB)
10:31:41  25/08/15 10:31:41 INFO MemoryStore: Block broadcast_1_piece0 stored as bytes in memory (estimated size 10.7 KiB, free 434.3 MiB)
10:31:41  25/08/15 10:31:41 INFO BlockManagerInfo: Added broadcast_1_piece0 in memory on ************:40200 (size: 10.7 KiB, free: 434.4 MiB)
10:31:41  25/08/15 10:31:41 INFO SparkContext: Created broadcast 1 from broadcast at DAGScheduler.scala:1585
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 1 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Adding task set 1.0 with 1 tasks resource profile 0
10:31:41  25/08/15 10:31:41 INFO TaskSetManager: Starting task 0.0 in stage 1.0 (TID 2) (**********, executor 0, partition 0, PROCESS_LOCAL, 11244 bytes)
10:31:41  25/08/15 10:31:41 INFO BlockManagerInfo: Added broadcast_1_piece0 in memory on **********:40200 (size: 10.7 KiB, free: 434.4 MiB)
10:31:41  25/08/15 10:31:41 INFO TaskSetManager: Finished task 0.0 in stage 1.0 (TID 2) in 210 ms on ********** (executor 0) (1/1)
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Removed TaskSet 1.0, whose tasks have all completed, from pool
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: ResultStage 1 (showString at NativeMethodAccessorImpl.java:0) finished in 0.220 s
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Job 1 is finished. Cancelling potential speculative or zombie tasks for this job
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Killing all running tasks in stage 1: Stage finished
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Job 1 finished: showString at NativeMethodAccessorImpl.java:0, took 0.225146 s
10:31:41  25/08/15 10:31:41 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Got job 2 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Final stage: ResultStage 2 (showString at NativeMethodAccessorImpl.java:0)
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Parents of final stage: List()
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Missing parents: List()
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Submitting ResultStage 2 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
10:31:41  25/08/15 10:31:41 INFO MemoryStore: Block broadcast_2 stored as values in memory (estimated size 25.4 KiB, free 434.3 MiB)
10:31:41  25/08/15 10:31:41 INFO MemoryStore: Block broadcast_2_piece0 stored as bytes in memory (estimated size 10.7 KiB, free 434.3 MiB)
10:31:41  25/08/15 10:31:41 INFO BlockManagerInfo: Added broadcast_2_piece0 in memory on ************:40200 (size: 10.7 KiB, free: 434.4 MiB)
10:31:41  25/08/15 10:31:41 INFO SparkContext: Created broadcast 2 from broadcast at DAGScheduler.scala:1585
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 2 (MapPartitionsRDD[12] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(1))
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Adding task set 2.0 with 1 tasks resource profile 0
10:31:41  25/08/15 10:31:41 INFO TaskSetManager: Starting task 0.0 in stage 2.0 (TID 3) (**********, executor 1, partition 1, PROCESS_LOCAL, 13231 bytes)
10:31:41  25/08/15 10:31:41 INFO BlockManagerInfo: Added broadcast_2_piece0 in memory on **********:40200 (size: 10.7 KiB, free: 434.4 MiB)
10:31:41  25/08/15 10:31:41 INFO TaskSetManager: Finished task 0.0 in stage 2.0 (TID 3) in 262 ms on ********** (executor 1) (1/1)
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Removed TaskSet 2.0, whose tasks have all completed, from pool
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: ResultStage 2 (showString at NativeMethodAccessorImpl.java:0) finished in 0.270 s
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Job 2 is finished. Cancelling potential speculative or zombie tasks for this job
10:31:41  25/08/15 10:31:41 INFO TaskSchedulerImpl: Killing all running tasks in stage 2: Stage finished
10:31:41  25/08/15 10:31:41 INFO DAGScheduler: Job 2 finished: showString at NativeMethodAccessorImpl.java:0, took 0.274256 s
10:31:42  25/08/15 10:31:42 INFO CodeGenerator: Code generated in 13.457998 ms
10:31:42  +-------------------+
10:31:42  |      key_as_string|
10:31:42  +-------------------+
10:31:42  |2025-08-12 10:00:00|
10:31:42  |2025-08-12 11:00:00|
10:31:42  |2025-08-12 12:00:00|
10:31:42  |2025-08-12 13:00:00|
10:31:42  |2025-08-12 14:00:00|
10:31:42  |2025-08-12 15:00:00|
10:31:42  |2025-08-12 16:00:00|
10:31:42  |2025-08-12 17:00:00|
10:31:42  |2025-08-12 18:00:00|
10:31:42  |2025-08-12 19:00:00|
10:31:42  |2025-08-12 20:00:00|
10:31:42  |2025-08-12 21:00:00|
10:31:42  |2025-08-12 22:00:00|
10:31:42  |2025-08-12 23:00:00|
10:31:42  |2025-08-13 00:00:00|
10:31:42  |2025-08-13 01:00:00|
10:31:42  |2025-08-13 02:00:00|
10:31:42  |2025-08-13 03:00:00|
10:31:42  |2025-08-13 04:00:00|
10:31:42  |2025-08-13 05:00:00|
10:31:42  +-------------------+
10:31:42  only showing top 20 rows
10:31:42
10:31:42  25/08/15 10:31:42 INFO CodeGenerator: Code generated in 120.496727 ms
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Registering RDD 15 (showString at NativeMethodAccessorImpl.java:0) as input to shuffle 0
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Got map stage job 3 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Final stage: ShuffleMapStage 3 (showString at NativeMethodAccessorImpl.java:0)
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Parents of final stage: List()
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Missing parents: List()
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Submitting ShuffleMapStage 3 (MapPartitionsRDD[15] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
10:31:42  25/08/15 10:31:42 INFO MemoryStore: Block broadcast_3 stored as values in memory (estimated size 50.0 KiB, free 434.2 MiB)
10:31:42  25/08/15 10:31:42 INFO MemoryStore: Block broadcast_3_piece0 stored as bytes in memory (estimated size 22.0 KiB, free 434.2 MiB)
10:31:42  25/08/15 10:31:42 INFO BlockManagerInfo: Added broadcast_3_piece0 in memory on ************:40200 (size: 22.0 KiB, free: 434.3 MiB)
10:31:42  25/08/15 10:31:42 INFO SparkContext: Created broadcast 3 from broadcast at DAGScheduler.scala:1585
10:31:42  25/08/15 10:31:42 INFO DAGScheduler: Submitting 1 missing tasks from ShuffleMapStage 3 (MapPartitionsRDD[15] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
10:31:42  25/08/15 10:31:42 INFO TaskSchedulerImpl: Adding task set 3.0 with 1 tasks resource profile 0
10:31:42  25/08/15 10:31:42 INFO TaskSetManager: Starting task 0.0 in stage 3.0 (TID 4) (**********, executor 0, partition 0, PROCESS_LOCAL, 11099 bytes)
10:31:42  25/08/15 10:31:42 INFO BlockManagerInfo: Added broadcast_3_piece0 in memory on **********:40200 (size: 22.0 KiB, free: 434.4 MiB)
10:32:08  25/08/15 10:32:08 INFO TaskSetManager: Finished task 0.0 in stage 3.0 (TID 4) in 25317 ms on ********** (executor 0) (1/1)
10:32:08  25/08/15 10:32:08 INFO TaskSchedulerImpl: Removed TaskSet 3.0, whose tasks have all completed, from pool
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: ShuffleMapStage 3 (showString at NativeMethodAccessorImpl.java:0) finished in 25.336 s
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: looking for newly runnable stages
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: running: Set()
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: waiting: Set()
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: failed: Set()
10:32:08  25/08/15 10:32:08 INFO ShufflePartitionsUtil: For shuffle(0), advisory target size: 67108864, actual target size 1048576, minimum partition size: 1048576
10:32:08  25/08/15 10:32:08 INFO HashAggregateExec: spark.sql.codegen.aggregate.map.twolevel.enabled is set to true, but current version of codegened fast hashmap does not support this aggregate.
10:32:08  25/08/15 10:32:08 INFO CodeGenerator: Code generated in 35.852068 ms
10:32:08  25/08/15 10:32:08 INFO SparkContext: Starting job: showString at NativeMethodAccessorImpl.java:0
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Got job 4 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Final stage: ResultStage 5 (showString at NativeMethodAccessorImpl.java:0)
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Parents of final stage: List(ShuffleMapStage 4)
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Missing parents: List()
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Submitting ResultStage 5 (MapPartitionsRDD[18] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
10:32:08  25/08/15 10:32:08 INFO MemoryStore: Block broadcast_4 stored as values in memory (estimated size 53.6 KiB, free 434.2 MiB)
10:32:08  25/08/15 10:32:08 INFO MemoryStore: Block broadcast_4_piece0 stored as bytes in memory (estimated size 23.1 KiB, free 434.1 MiB)
10:32:08  25/08/15 10:32:08 INFO BlockManagerInfo: Added broadcast_4_piece0 in memory on ************:40200 (size: 23.1 KiB, free: 434.3 MiB)
10:32:08  25/08/15 10:32:08 INFO SparkContext: Created broadcast 4 from broadcast at DAGScheduler.scala:1585
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 5 (MapPartitionsRDD[18] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
10:32:08  25/08/15 10:32:08 INFO TaskSchedulerImpl: Adding task set 5.0 with 1 tasks resource profile 0
10:32:08  25/08/15 10:32:08 INFO TaskSetManager: Starting task 0.0 in stage 5.0 (TID 5) (**********, executor 0, partition 0, NODE_LOCAL, 11264 bytes)
10:32:08  25/08/15 10:32:08 INFO BlockManagerInfo: Added broadcast_4_piece0 in memory on **********:40200 (size: 23.1 KiB, free: 434.3 MiB)
10:32:08  25/08/15 10:32:08 INFO MapOutputTrackerMasterEndpoint: Asked to send map output locations for shuffle 0 to 2.49.0.0:31501
10:32:08  25/08/15 10:32:08 INFO TaskSetManager: Finished task 0.0 in stage 5.0 (TID 5) in 186 ms on ********** (executor 0) (1/1)
10:32:08  25/08/15 10:32:08 INFO TaskSchedulerImpl: Removed TaskSet 5.0, whose tasks have all completed, from pool
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: ResultStage 5 (showString at NativeMethodAccessorImpl.java:0) finished in 0.199 s
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Job 4 is finished. Cancelling potential speculative or zombie tasks for this job
10:32:08  25/08/15 10:32:08 INFO TaskSchedulerImpl: Killing all running tasks in stage 5: Stage finished
10:32:08  25/08/15 10:32:08 INFO DAGScheduler: Job 4 finished: showString at NativeMethodAccessorImpl.java:0, took 0.212457 s
10:32:08  25/08/15 10:32:08 INFO CodeGenerator: Code generated in 10.569299 ms
10:32:08  +----------+-----------+-----+-------------------+
10:32:08  |news_count|wxpyq_count|total|               hour|
10:32:08  +----------+-----------+-----+-------------------+
10:32:08  |        47|          0|   47|2025-08-14 19:00:00|
10:32:08  |         9|          0|    9|2025-08-14 20:00:00|
10:32:08  |        41|          0|   41|2025-08-13 21:00:00|
10:32:08  |         0|          0|    0|2025-08-12 18:00:00|
10:32:08  |        66|          0|   66|2025-08-15 02:00:00|
10:32:08  |         4|          0|    4|2025-08-14 06:00:00|
10:32:08  |        36|          0|   36|2025-08-14 01:00:00|
10:32:08  |        23|          0|   23|2025-08-12 13:00:00|
10:32:08  |         4|          0|    4|2025-08-15 09:00:00|
10:32:08  |         0|          0|    0|2025-08-13 01:00:00|
10:32:08  +----------+-----------+-----+-------------------+
10:32:08  only showing top 10 rows
10:32:08
10:32:09  25/08/15 10:32:09 INFO CodeGenerator: Code generated in 15.54213 ms
10:32:09  25/08/15 10:32:09 INFO CodeGenerator: Code generated in 15.204656 ms
10:32:09  25/08/15 10:32:09 INFO SparkContext: Starting job: $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Got job 5 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) with 1 output partitions
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Final stage: ResultStage 6 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264)
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Parents of final stage: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Missing parents: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting ResultStage 6 (MapPartitionsRDD[23] at $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264), which has no missing parents
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_5 stored as values in memory (estimated size 28.5 KiB, free 434.1 MiB)
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_5_piece0 stored as bytes in memory (estimated size 13.6 KiB, free 434.1 MiB)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_5_piece0 in memory on ************:40200 (size: 13.6 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO SparkContext: Created broadcast 5 from broadcast at DAGScheduler.scala:1585
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 6 (MapPartitionsRDD[23] at $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) (first 15 tasks are for partitions Vector(0))
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Adding task set 6.0 with 1 tasks resource profile 0
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Starting task 0.0 in stage 6.0 (TID 6) (**********, executor 0, partition 0, PROCESS_LOCAL, 11110 bytes)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_5_piece0 in memory on **********:40200 (size: 13.6 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Finished task 0.0 in stage 6.0 (TID 6) in 206 ms on ********** (executor 0) (1/1)
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Removed TaskSet 6.0, whose tasks have all completed, from pool
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: ResultStage 6 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) finished in 0.222 s
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Job 5 is finished. Cancelling potential speculative or zombie tasks for this job
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Killing all running tasks in stage 6: Stage finished
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Job 5 finished: $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264, took 0.225856 s
10:32:09  25/08/15 10:32:09 INFO CodeGenerator: Code generated in 8.144844 ms
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Registering RDD 25 (showString at NativeMethodAccessorImpl.java:0) as input to shuffle 1
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Got map stage job 6 (showString at NativeMethodAccessorImpl.java:0) with 2 output partitions
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Final stage: ShuffleMapStage 7 (showString at NativeMethodAccessorImpl.java:0)
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Parents of final stage: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Missing parents: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting ShuffleMapStage 7 (MapPartitionsRDD[25] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_6 stored as values in memory (estimated size 27.9 KiB, free 434.1 MiB)
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_6_piece0 stored as bytes in memory (estimated size 11.9 KiB, free 434.1 MiB)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_6_piece0 in memory on ************:40200 (size: 11.9 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO SparkContext: Created broadcast 6 from broadcast at DAGScheduler.scala:1585
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting 2 missing tasks from ShuffleMapStage 7 (MapPartitionsRDD[25] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0, 1))
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Adding task set 7.0 with 2 tasks resource profile 0
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Starting task 0.0 in stage 7.0 (TID 7) (**********, executor 0, partition 0, PROCESS_LOCAL, 11233 bytes)
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Starting task 1.0 in stage 7.0 (TID 8) (**********, executor 1, partition 1, PROCESS_LOCAL, 13220 bytes)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_6_piece0 in memory on **********:40200 (size: 11.9 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_6_piece0 in memory on **********:40200 (size: 11.9 KiB, free: 434.4 MiB)
10:32:09  25/08/15 10:32:09 INFO CodeGenerator: Code generated in 58.44555 ms
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Registering RDD 27 (showString at NativeMethodAccessorImpl.java:0) as input to shuffle 2
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Got map stage job 7 (showString at NativeMethodAccessorImpl.java:0) with 1 output partitions
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Final stage: ShuffleMapStage 8 (showString at NativeMethodAccessorImpl.java:0)
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Parents of final stage: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Missing parents: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting ShuffleMapStage 8 (MapPartitionsRDD[27] at showString at NativeMethodAccessorImpl.java:0), which has no missing parents
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_7 stored as values in memory (estimated size 61.1 KiB, free 434.0 MiB)
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_7_piece0 stored as bytes in memory (estimated size 26.4 KiB, free 434.0 MiB)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_7_piece0 in memory on ************:40200 (size: 26.4 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO SparkContext: Created broadcast 7 from broadcast at DAGScheduler.scala:1585
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting 1 missing tasks from ShuffleMapStage 8 (MapPartitionsRDD[27] at showString at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Adding task set 8.0 with 1 tasks resource profile 0
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Starting task 0.0 in stage 8.0 (TID 9) (**********, executor 1, partition 0, PROCESS_LOCAL, 11099 bytes)
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Finished task 0.0 in stage 7.0 (TID 7) in 91 ms on ********** (executor 0) (1/2)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_7_piece0 in memory on **********:40200 (size: 26.4 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Finished task 1.0 in stage 7.0 (TID 8) in 272 ms on ********** (executor 1) (2/2)
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Removed TaskSet 7.0, whose tasks have all completed, from pool
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: ShuffleMapStage 7 (showString at NativeMethodAccessorImpl.java:0) finished in 0.282 s
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: looking for newly runnable stages
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: running: Set(ShuffleMapStage 8)
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: waiting: Set()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: failed: Set()
10:32:09  +----------+-----------+-----+----+---+-----+----+-----------+------+
10:32:09  |news_count|wxpyq_count|total|hour|day|month|year|insert_time|source|
10:32:09  +----------+-----------+-----+----+---+-----+----+-----------+------+
10:32:09  +----------+-----------+-----+----+---+-----+----+-----------+------+
10:32:09
10:32:09  25/08/15 10:32:09 INFO ApacheHttpConnectionImpl: Connection TTL: 0 ms
10:32:09  25/08/15 10:32:09 INFO ApacheHttpConnectionImpl: Connection reuse strategy: LIFO
10:32:09  25/08/15 10:32:09 INFO SparkContext: Starting job: $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Got job 8 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) with 1 output partitions
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Final stage: ResultStage 9 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264)
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Parents of final stage: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Missing parents: List()
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting ResultStage 9 (MapPartitionsRDD[32] at $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264), which has no missing parents
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_8 stored as values in memory (estimated size 28.5 KiB, free 434.0 MiB)
10:32:09  25/08/15 10:32:09 INFO MemoryStore: Block broadcast_8_piece0 stored as bytes in memory (estimated size 13.6 KiB, free 433.9 MiB)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_8_piece0 in memory on ************:40200 (size: 13.6 KiB, free: 434.3 MiB)
10:32:09  25/08/15 10:32:09 INFO SparkContext: Created broadcast 8 from broadcast at DAGScheduler.scala:1585
10:32:09  25/08/15 10:32:09 INFO DAGScheduler: Submitting 1 missing tasks from ResultStage 9 (MapPartitionsRDD[32] at $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) (first 15 tasks are for partitions Vector(0))
10:32:09  25/08/15 10:32:09 INFO TaskSchedulerImpl: Adding task set 9.0 with 1 tasks resource profile 0
10:32:09  25/08/15 10:32:09 INFO TaskSetManager: Starting task 0.0 in stage 9.0 (TID 10) (**********, executor 1, partition 0, PROCESS_LOCAL, 11110 bytes)
10:32:09  25/08/15 10:32:09 INFO BlockManagerInfo: Added broadcast_8_piece0 in memory on **********:40200 (size: 13.6 KiB, free: 434.3 MiB)
10:32:10  25/08/15 10:32:10 INFO TaskSetManager: Finished task 0.0 in stage 9.0 (TID 10) in 462 ms on ********** (executor 1) (1/1)
10:32:10  25/08/15 10:32:10 INFO TaskSchedulerImpl: Removed TaskSet 9.0, whose tasks have all completed, from pool
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: ResultStage 9 ($anonfun$withThreadLocalCaptured$1 at FutureTask.java:264) finished in 0.469 s
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Job 8 is finished. Cancelling potential speculative or zombie tasks for this job
10:32:10  25/08/15 10:32:10 INFO TaskSchedulerImpl: Killing all running tasks in stage 9: Stage finished
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Job 8 finished: $anonfun$withThreadLocalCaptured$1 at FutureTask.java:264, took 0.472356 s
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Registering RDD 34 (save at NativeMethodAccessorImpl.java:0) as input to shuffle 3
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Got map stage job 9 (save at NativeMethodAccessorImpl.java:0) with 2 output partitions
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Final stage: ShuffleMapStage 10 (save at NativeMethodAccessorImpl.java:0)
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Parents of final stage: List()
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Missing parents: List()
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Submitting ShuffleMapStage 10 (MapPartitionsRDD[34] at save at NativeMethodAccessorImpl.java:0), which has no missing parents
10:32:10  25/08/15 10:32:10 INFO MemoryStore: Block broadcast_9 stored as values in memory (estimated size 27.9 KiB, free 433.9 MiB)
10:32:10  25/08/15 10:32:10 INFO MemoryStore: Block broadcast_9_piece0 stored as bytes in memory (estimated size 11.9 KiB, free 433.9 MiB)
10:32:10  25/08/15 10:32:10 INFO BlockManagerInfo: Added broadcast_9_piece0 in memory on ************:40200 (size: 11.9 KiB, free: 434.2 MiB)
10:32:10  25/08/15 10:32:10 INFO SparkContext: Created broadcast 9 from broadcast at DAGScheduler.scala:1585
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Submitting 2 missing tasks from ShuffleMapStage 10 (MapPartitionsRDD[34] at save at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0, 1))
10:32:10  25/08/15 10:32:10 INFO TaskSchedulerImpl: Adding task set 10.0 with 2 tasks resource profile 0
10:32:10  25/08/15 10:32:10 INFO TaskSetManager: Starting task 0.0 in stage 10.0 (TID 11) (**********, executor 1, partition 0, PROCESS_LOCAL, 11233 bytes)
10:32:10  25/08/15 10:32:10 INFO TaskSetManager: Starting task 1.0 in stage 10.0 (TID 12) (**********, executor 0, partition 1, PROCESS_LOCAL, 13220 bytes)
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Registering RDD 36 (save at NativeMethodAccessorImpl.java:0) as input to shuffle 4
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Got map stage job 10 (save at NativeMethodAccessorImpl.java:0) with 1 output partitions
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Final stage: ShuffleMapStage 11 (save at NativeMethodAccessorImpl.java:0)
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Parents of final stage: List()
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Missing parents: List()
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Submitting ShuffleMapStage 11 (MapPartitionsRDD[36] at save at NativeMethodAccessorImpl.java:0), which has no missing parents
10:32:10  25/08/15 10:32:10 INFO MemoryStore: Block broadcast_10 stored as values in memory (estimated size 61.1 KiB, free 433.8 MiB)
10:32:10  25/08/15 10:32:10 INFO BlockManagerInfo: Added broadcast_9_piece0 in memory on **********:40200 (size: 11.9 KiB, free: 434.3 MiB)
10:32:10  25/08/15 10:32:10 INFO BlockManagerInfo: Added broadcast_9_piece0 in memory on **********:40200 (size: 11.9 KiB, free: 434.3 MiB)
10:32:10  25/08/15 10:32:10 INFO MemoryStore: Block broadcast_10_piece0 stored as bytes in memory (estimated size 26.4 KiB, free 433.8 MiB)
10:32:10  25/08/15 10:32:10 INFO BlockManagerInfo: Added broadcast_10_piece0 in memory on ************:40200 (size: 26.4 KiB, free: 434.2 MiB)
10:32:10  25/08/15 10:32:10 INFO SparkContext: Created broadcast 10 from broadcast at DAGScheduler.scala:1585
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Submitting 1 missing tasks from ShuffleMapStage 11 (MapPartitionsRDD[36] at save at NativeMethodAccessorImpl.java:0) (first 15 tasks are for partitions Vector(0))
10:32:10  25/08/15 10:32:10 INFO TaskSchedulerImpl: Adding task set 11.0 with 1 tasks resource profile 0
10:32:10  25/08/15 10:32:10 INFO TaskSetManager: Starting task 0.0 in stage 11.0 (TID 13) (**********, executor 0, partition 0, PROCESS_LOCAL, 11099 bytes)
10:32:10  25/08/15 10:32:10 INFO BlockManagerInfo: Added broadcast_10_piece0 in memory on **********:40200 (size: 26.4 KiB, free: 434.3 MiB)
10:32:10  25/08/15 10:32:10 INFO TaskSetManager: Finished task 0.0 in stage 10.0 (TID 11) in 68 ms on ********** (executor 1) (1/2)
10:32:10  25/08/15 10:32:10 INFO TaskSetManager: Finished task 1.0 in stage 10.0 (TID 12) in 116 ms on ********** (executor 0) (2/2)
10:32:10  25/08/15 10:32:10 INFO TaskSchedulerImpl: Removed TaskSet 10.0, whose tasks have all completed, from pool
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: ShuffleMapStage 10 (save at NativeMethodAccessorImpl.java:0) finished in 0.123 s
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: looking for newly runnable stages
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: running: Set(ShuffleMapStage 11, ShuffleMapStage 8)
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: waiting: Set()
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: failed: Set()
10:32:10  25/08/15 10:32:10 INFO SparkContext: Starting job: save at NativeMethodAccessorImpl.java:0
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: Job 11 finished: save at NativeMethodAccessorImpl.java:0, took 0.000302 s
10:32:10  25/08/15 10:32:10 INFO ApacheHttpConnectionImpl: Connection TTL: 0 ms
10:32:10  25/08/15 10:32:10 INFO ApacheHttpConnectionImpl: Connection reuse strategy: LIFO
10:32:10  25/08/15 10:32:10 INFO SparkContext: SparkContext is stopping with exitCode 0.
10:32:10  25/08/15 10:32:10 INFO SparkUI: Stopped Spark web UI at http://************:4040
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: ShuffleMapStage 11 (save at NativeMethodAccessorImpl.java:0) failed in 0.206 s due to Stage cancelled because SparkContext was shut down
10:32:10  25/08/15 10:32:10 INFO DAGScheduler: ShuffleMapStage 8 (showString at NativeMethodAccessorImpl.java:0) failed in 1.131 s due to Stage cancelled because SparkContext was shut down
10:32:10  25/08/15 10:32:10 INFO StandaloneSchedulerBackend: Shutting down all executors
10:32:10  25/08/15 10:32:10 INFO StandaloneSchedulerBackend$StandaloneDriverEndpoint: Asking each executor to shut down
10:32:10  25/08/15 10:32:10 INFO MapOutputTrackerMasterEndpoint: MapOutputTrackerMasterEndpoint stopped!
10:32:10  25/08/15 10:32:10 INFO MemoryStore: MemoryStore cleared
10:32:10  25/08/15 10:32:10 INFO BlockManager: BlockManager stopped
10:32:10  25/08/15 10:32:10 INFO BlockManagerMaster: BlockManagerMaster stopped
10:32:10  25/08/15 10:32:10 INFO OutputCommitCoordinator$OutputCommitCoordinatorEndpoint: OutputCommitCoordinator stopped!
10:32:10  25/08/15 10:32:10 INFO SparkContext: Successfully stopped SparkContext
10:32:11  25/08/15 10:32:11 INFO ShutdownHookManager: Shutdown hook called
10:32:11  25/08/15 10:32:11 INFO ShutdownHookManager: Deleting directory /tmp/spark-078bda7e-4bc3-4cb8-a195-fe821ec832ca/pyspark-6f83dd96-03c7-4a77-885c-34903ec51d0c
10:32:11  25/08/15 10:32:11 INFO ShutdownHookManager: Deleting directory /tmp/spark-c87aa74d-62ae-41ca-8a07-b1b1fbc46524
10:32:11  25/08/15 10:32:11 INFO ShutdownHookManager: Deleting directory /tmp/spark-078bda7e-4bc3-4cb8-a195-fe821ec832ca
10:32:11  执行任务结束：任务01
==================================================