#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版本的西藏2.py，专门用于测试protobuf错误修复
只包含核心的查询逻辑，便于快速测试和调试
"""

from hybaseapi.TRSConnection import TRSConnection
from hybaseapi.ConnectParams import ConnectParams
from datetime import datetime, timedelta
import time
import traceback

# 配置 Hybase 数据库连接
host = "***********"
port = "5555"
username = "admin"
password = "trs@300229"

# 问题表列表
PROBLEMATIC_TABLES = set()

def create_hybase_connection(host: str, port: int, username: str, password: str):
    """创建Hybase数据库连接"""
    url = f"http://{host.split(',')[0]}:{port}"
    return TRSConnection(url, username, password, ConnectParams())

def clean_query_condition(where: str):
    """清理查询条件"""
    if not where:
        return where
    return ' '.join(where.split()).strip()

def add_problematic_table(table_name: str):
    """将表添加到问题表列表中"""
    PROBLEMATIC_TABLES.add(table_name)
    print(f"表 {table_name} 已添加到问题表列表中，后续查询将跳过")

def is_problematic_table(table_name: str):
    """检查表是否在问题表列表中"""
    return table_name in PROBLEMATIC_TABLES

def safe_category_query_with_reconnect(table_name: str, where: str, group_field: str, max_results: int = 10000, max_retries: int = 3):
    """
    安全的分类查询方法，每次重试都重新建立连接
    专门用于测试protobuf错误修复
    """
    # 检查是否为问题表
    if is_problematic_table(table_name):
        print(f"跳过问题表 {table_name}")
        return None
    
    consecutive_protobuf_errors = 0
    
    for attempt in range(max_retries):
        conn = None
        try:
            print(f"正在查询表 {table_name}，尝试第 {attempt + 1} 次...")
            
            # 每次重试都重新建立连接
            conn = create_hybase_connection(host, port, username, password)
            
            # 清理查询条件
            cleaned_where = clean_query_condition(where)
            print(f"使用查询条件: {cleaned_where}")
            
            # 执行查询
            resultSet = conn.categoryQuery(table_name, cleaned_where, None, group_field, max_results)
            
            # 验证结果
            if resultSet is not None:
                num_found = resultSet.getNumFound()
                print(f"✅ 查询成功，找到 {num_found} 条记录")
                return resultSet
            else:
                print("⚠️ 查询返回空结果")
                return None
                
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 查询表 {table_name} 失败，尝试第 {attempt + 1} 次")
            print(f"错误信息: {error_msg}")
            
            # 特殊处理protobuf错误
            if "DecodeError" in error_msg or "protobuf" in error_msg.lower():
                consecutive_protobuf_errors += 1
                print(f"🔍 检测到protobuf解析错误，连续错误次数: {consecutive_protobuf_errors}")
                
                # 如果连续2次protobuf错误，将表加入问题列表（测试版本降低阈值）
                if consecutive_protobuf_errors >= 2:
                    print(f"🚫 表 {table_name} 连续出现protobuf错误，加入问题表列表")
                    add_problematic_table(table_name)
                    return None
            else:
                consecutive_protobuf_errors = 0
            
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 3  # 3秒递增等待
                print(f"⏳ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"💥 查询表 {table_name} 最终失败，已达到最大重试次数")
                if consecutive_protobuf_errors > 0:
                    add_problematic_table(table_name)
                return None
                
        finally:
            # 确保连接被正确关闭
            if conn is not None:
                try:
                    if hasattr(conn, 'close'):
                        conn.close()
                except:
                    pass
    return None

def test_single_table(table_name: str, where: str):
    """测试单个表的查询"""
    print(f"\n{'='*60}")
    print(f"测试表: {table_name}")
    print(f"{'='*60}")
    
    result = safe_category_query_with_reconnect(
        table_name=table_name,
        where=where,
        group_field="trs_m_publish_hour",
        max_results=1000,  # 测试用小数据量
        max_retries=3
    )
    
    if result is not None:
        print(f"✅ 表 {table_name} 测试成功")
        return True
    else:
        print(f"❌ 表 {table_name} 测试失败")
        return False

def main():
    """主测试函数"""
    print("🚀 开始protobuf错误修复测试")
    print("="*60)
    
    # 测试时间范围（最近1小时）
    end_time = datetime.now()
    begin_time = end_time - timedelta(hours=1)
    where = f"m_publish_time:[{int(begin_time.timestamp()*1000)} TO {int(end_time.timestamp()*1000)}] AND g_asp:trsdc"
    
    print(f"测试时间范围: {where}")
    
    # 测试表列表（从最容易出问题的开始）
    test_tables = [
        "microblog.tc_msg_microblog",  # 微博
        "other.tc_msg_news",  # 新闻
        "other.tc_msg_selfmedia",  # 自媒体
        "wechat.tc_msg_wechat_public_group_20201225",  # 微信（需要字段替换）
    ]
    
    success_count = 0
    total_count = len(test_tables)
    
    for table_name in test_tables:
        # 微信表需要特殊处理
        if table_name == "wechat.tc_msg_wechat_public_group_20201225":
            test_where = where.replace("g_asp:", "trs_g_asp:")
        else:
            test_where = where
            
        success = test_single_table(table_name, test_where)
        if success:
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"测试完成")
    print(f"成功: {success_count}/{total_count}")
    print(f"问题表列表: {PROBLEMATIC_TABLES}")
    print(f"{'='*60}")
    
    if success_count > 0:
        print("🎉 至少有部分表查询成功，修复方案有效！")
    else:
        print("😞 所有表都查询失败，可能需要进一步调试")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"💥 程序执行过程中发生错误: {str(e)}")
        traceback.print_exc()
